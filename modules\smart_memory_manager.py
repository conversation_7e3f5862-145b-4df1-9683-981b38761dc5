#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能记忆管理模块
负责记忆规则的CRUD操作、数据持久化和自动匹配逻辑
"""

import json
import os
import shutil
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class MemoryRule:
    """记忆规则数据结构"""
    sku_code: str
    color_spec: str
    price: float
    selected_colors: List[str]
    supplier: str = ""
    file_pattern: str = ""
    created_time: str = ""
    last_used: str = ""
    usage_count: int = 0
    confidence: float = 1.0
    notes: str = ""


class SmartMemoryManager:
    """智能记忆管理器"""
    
    def __init__(self, config_manager=None, logger=None, memory_file: str = "data/smart_memory.json"):
        """
        初始化智能记忆管理器
        
        Args:
            config_manager: 配置管理器实例
            logger: 日志记录器实例
            memory_file: 记忆数据文件路径
        """
        self.config_manager = config_manager
        self.logger = logger
        self.memory_file = memory_file
        self.memory_cache = {}  # 内存缓存
        self.settings = {}
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(memory_file), exist_ok=True)
        
        # 加载记忆规则
        self.load_memory_rules()
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        if self.logger:
            if hasattr(self.logger, 'log_message'):
                self.logger.log_message(message, level)
            else:
                print(f"[{level}] {message}")
        else:
            print(f"[{level}] {message}")
    
    def load_memory_rules(self) -> bool:
        """从文件加载记忆规则到内存缓存"""
        try:
            if not os.path.exists(self.memory_file):
                # 创建默认文件
                self._create_default_memory_file()
                return True
            
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载设置
            self.settings = data.get('settings', self._get_default_settings())
            
            # 加载记忆规则到缓存
            memories = data.get('memories', {})
            self.memory_cache = {}
            
            for rule_key, rule_data in memories.items():
                try:
                    memory_rule = MemoryRule(**rule_data)
                    self.memory_cache[rule_key] = memory_rule
                except Exception as e:
                    self.log(f"加载记忆规则失败: {rule_key} - {str(e)}", "WARNING")
            
            self.log(f"成功加载 {len(self.memory_cache)} 条记忆规则", "DEBUG")
            return True
            
        except Exception as e:
            self.log(f"加载记忆规则文件失败: {str(e)}", "ERROR")
            self.memory_cache = {}
            self.settings = self._get_default_settings()
            return False
    
    def save_memory_rules(self) -> bool:
        """保存记忆规则到文件"""
        try:
            # 备份现有文件
            if os.path.exists(self.memory_file) and self.settings.get('backup_enabled', True):
                backup_file = f"{self.memory_file}.backup"
                shutil.copy2(self.memory_file, backup_file)
            
            # 准备保存数据
            memories_data = {}
            for rule_key, memory_rule in self.memory_cache.items():
                memories_data[rule_key] = asdict(memory_rule)
            
            save_data = {
                "version": "1.0",
                "last_updated": datetime.now().isoformat(),
                "settings": self.settings,
                "memories": memories_data
            }
            
            # 保存到文件
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            self.log(f"成功保存 {len(self.memory_cache)} 条记忆规则", "DEBUG")
            return True
            
        except Exception as e:
            self.log(f"保存记忆规则失败: {str(e)}", "ERROR")
            return False
    
    def save_memory_rule(self, sku_code: str, color_spec: str, price: float, 
                        selected_colors: List[str], supplier: str = "", 
                        file_pattern: str = "", notes: str = "") -> bool:
        """
        保存新的记忆规则
        
        Args:
            sku_code: 商品款号
            color_spec: 颜色规格
            price: 单价
            selected_colors: 用户选择的颜色列表
            supplier: 供应商
            file_pattern: 文件模式
            notes: 备注
            
        Returns:
            是否保存成功
        """
        try:
            if not self.settings.get('enabled', True):
                self.log("智能记忆功能已禁用", "DEBUG")
                return False
            
            # 生成记忆规则键
            rule_key = self._generate_rule_key(sku_code, color_spec, price)
            
            # 检查是否已存在
            if rule_key in self.memory_cache:
                # 更新现有规则
                existing_rule = self.memory_cache[rule_key]
                existing_rule.selected_colors = selected_colors
                existing_rule.last_used = datetime.now().isoformat()
                existing_rule.usage_count += 1
                existing_rule.supplier = supplier or existing_rule.supplier
                existing_rule.file_pattern = file_pattern or existing_rule.file_pattern
                existing_rule.notes = notes or existing_rule.notes
                self.log(f"更新记忆规则: {rule_key}", "DEBUG")
            else:
                # 创建新规则
                memory_rule = MemoryRule(
                    sku_code=sku_code,
                    color_spec=color_spec,
                    price=price,
                    selected_colors=selected_colors,
                    supplier=supplier,
                    file_pattern=file_pattern,
                    created_time=datetime.now().isoformat(),
                    last_used=datetime.now().isoformat(),
                    usage_count=1,
                    confidence=1.0,
                    notes=notes
                )
                self.memory_cache[rule_key] = memory_rule
                self.log(f"创建记忆规则: {rule_key}", "DEBUG")
            
            # 检查记忆规则数量限制
            max_memories = self.settings.get('max_memories', 1000)
            if len(self.memory_cache) > max_memories:
                self._cleanup_old_memories(max_memories)
            
            # 保存到文件
            return self.save_memory_rules()
            
        except Exception as e:
            self.log(f"保存记忆规则失败: {str(e)}", "ERROR")
            return False
    
    def check_memory_rules(self, sku_code: str, color_spec: str, price: float) -> Optional[MemoryRule]:
        """
        检查是否有匹配的记忆规则
        
        Args:
            sku_code: 商品款号
            color_spec: 颜色规格
            price: 单价
            
        Returns:
            匹配的记忆规则，如果没有匹配则返回None
        """
        try:
            if not self.settings.get('enabled', True):
                return None
            
            # 精确匹配
            exact_key = self._generate_rule_key(sku_code, color_spec, price)
            if exact_key in self.memory_cache:
                rule = self.memory_cache[exact_key]
                self._update_rule_usage(exact_key)
                self.log(f"精确匹配记忆规则: {exact_key}", "DEBUG")
                return rule
            
            # 价格容差匹配（±5%）
            price_tolerance = 0.05
            for rule_key, memory_rule in self.memory_cache.items():
                if (memory_rule.sku_code == sku_code and 
                    memory_rule.color_spec == color_spec):
                    
                    price_diff = abs(memory_rule.price - price) / max(memory_rule.price, price)
                    if price_diff <= price_tolerance:
                        self._update_rule_usage(rule_key)
                        self.log(f"容差匹配记忆规则: {rule_key} (价格差异: {price_diff:.2%})", "DEBUG")
                        return memory_rule
            
            return None
            
        except Exception as e:
            self.log(f"检查记忆规则失败: {str(e)}", "ERROR")
            return None
    
    def get_all_memory_rules(self) -> Dict[str, MemoryRule]:
        """获取所有记忆规则"""
        return self.memory_cache.copy()
    
    def delete_memory_rule(self, rule_key: str) -> bool:
        """删除指定记忆规则"""
        try:
            if rule_key in self.memory_cache:
                del self.memory_cache[rule_key]
                self.log(f"删除记忆规则: {rule_key}", "DEBUG")
                return self.save_memory_rules()
            else:
                self.log(f"记忆规则不存在: {rule_key}", "WARNING")
                return False
        except Exception as e:
            self.log(f"删除记忆规则失败: {str(e)}", "ERROR")
            return False
    
    def update_memory_rule(self, rule_key: str, data: Dict[str, Any]) -> bool:
        """更新记忆规则"""
        try:
            if rule_key not in self.memory_cache:
                self.log(f"记忆规则不存在: {rule_key}", "WARNING")
                return False
            
            memory_rule = self.memory_cache[rule_key]
            for key, value in data.items():
                if hasattr(memory_rule, key):
                    setattr(memory_rule, key, value)
            
            self.log(f"更新记忆规则: {rule_key}", "DEBUG")
            return self.save_memory_rules()
            
        except Exception as e:
            self.log(f"更新记忆规则失败: {str(e)}", "ERROR")
            return False
    
    def get_settings(self) -> Dict[str, Any]:
        """获取设置"""
        return self.settings.copy()
    
    def update_settings(self, new_settings: Dict[str, Any]) -> bool:
        """更新设置"""
        try:
            self.settings.update(new_settings)
            return self.save_memory_rules()
        except Exception as e:
            self.log(f"更新设置失败: {str(e)}", "ERROR")
            return False
    
    def _generate_rule_key(self, sku_code: str, color_spec: str, price: float) -> str:
        """生成记忆规则键"""
        return f"{sku_code}|{color_spec}|{price:.2f}"
    
    def _update_rule_usage(self, rule_key: str):
        """更新规则使用统计"""
        if rule_key in self.memory_cache:
            rule = self.memory_cache[rule_key]
            rule.last_used = datetime.now().isoformat()
            rule.usage_count += 1
    
    def _cleanup_old_memories(self, max_count: int):
        """清理旧的记忆规则"""
        if len(self.memory_cache) <= max_count:
            return
        
        # 按最后使用时间排序，删除最旧的
        sorted_rules = sorted(
            self.memory_cache.items(),
            key=lambda x: x[1].last_used or x[1].created_time
        )
        
        to_remove = len(self.memory_cache) - max_count
        for i in range(to_remove):
            rule_key = sorted_rules[i][0]
            del self.memory_cache[rule_key]
            self.log(f"清理旧记忆规则: {rule_key}", "DEBUG")
    
    def _create_default_memory_file(self):
        """创建默认记忆文件"""
        default_data = {
            "version": "1.0",
            "last_updated": datetime.now().isoformat(),
            "settings": self._get_default_settings(),
            "memories": {}
        }
        
        with open(self.memory_file, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, indent=2, ensure_ascii=False)
        
        self.settings = default_data['settings']
        self.log("创建默认记忆文件", "DEBUG")
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """获取默认设置"""
        return {
            "enabled": True,
            "auto_save_on_upload": True,
            "auto_confirm_threshold": 0.8,
            "max_memories": 1000,
            "backup_enabled": True
        }
