#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能记忆功能演示脚本
展示智能记忆功能的核心特性和使用方法
"""

import os
import sys
import json
from datetime import datetime

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.smart_memory_manager import SmartMemoryManager


def demo_smart_memory_workflow():
    """演示智能记忆完整工作流程"""
    print("🎬 智能记忆功能演示")
    print("=" * 60)
    
    # 创建演示用的记忆管理器
    demo_file = "demo_smart_memory.json"
    if os.path.exists(demo_file):
        os.remove(demo_file)
    
    manager = SmartMemoryManager(demo_file)
    
    print("\n📋 场景：用户首次处理商品")
    print("-" * 40)
    
    # 模拟用户首次确认颜色
    demo_products = [
        {
            "sku_code": "7922",
            "color_spec": "套装",
            "confirmed_colors": ["两件套装"],
            "description": "两件套装商品"
        },
        {
            "sku_code": "1359",
            "color_spec": "连衣裙", 
            "confirmed_colors": ["白色吊带裙", "连衣裙"],
            "description": "连衣裙商品"
        },
        {
            "sku_code": "1096",
            "color_spec": "蓝色",
            "confirmed_colors": ["蓝色"],
            "description": "蓝色商品"
        }
    ]
    
    print("👤 用户正在确认颜色...")
    for i, product in enumerate(demo_products, 1):
        print(f"  {i}. 确认 {product['sku_code']} - {product['color_spec']}")
        print(f"     选择颜色: {', '.join(product['confirmed_colors'])}")
        
        # 添加到临时记忆
        memory_key = manager.add_temp_memory(
            sku_code=product["sku_code"],
            color_spec=product["color_spec"],
            confirmed_colors=product["confirmed_colors"],
            supplier="DEMO",
            file_pattern="demo_image.jpg",
            notes=f"演示商品{i}"
        )
        print(f"     ✅ 已添加到临时记忆: {memory_key}")
    
    print(f"\n📊 当前临时记忆数量: {len(manager.temp_memories)}")
    
    print("\n💾 场景：用户点击上传按钮")
    print("-" * 40)
    print("🚀 正在上传成本价到ERP系统...")
    print("✅ 上传成功！")
    
    # 提交临时记忆
    success = manager.commit_temp_memories()
    print(f"🧠 智能记忆已保存: {success}")
    
    # 显示统计信息
    stats = manager.get_memory_statistics()
    print(f"📈 总记忆数量: {stats.get('total_memories', 0)}")
    print(f"📈 临时记忆数量: {stats.get('temp_memories', 0)}")
    
    print("\n🔄 场景：用户再次处理相同商品")
    print("-" * 40)
    
    # 模拟再次加载相同商品
    for product in demo_products:
        print(f"\n🔍 加载商品: {product['sku_code']} - {product['color_spec']}")
        
        # 查找匹配记忆
        matching_memory = manager.find_matching_memory(
            product["sku_code"],
            product["color_spec"]
        )
        
        if matching_memory:
            print(f"  🧠 找到智能记忆！")
            print(f"     历史选择: {', '.join(matching_memory.confirmed_colors)}")
            print(f"     使用次数: {matching_memory.usage_count}")
            print(f"     置信度: {matching_memory.confidence:.2f}")
            print(f"  ⚡ 自动确认颜色选择")
            
            # 更新使用统计
            manager.update_memory_usage(product["sku_code"], product["color_spec"])
        else:
            print(f"  ❌ 未找到匹配记忆，需要手动确认")
    
    print("\n📊 场景：查看记忆管理界面")
    print("-" * 40)
    
    # 显示记忆列表
    print("🗂️ 记忆列表:")
    for key, memory in manager.memories.items():
        print(f"  • {memory.sku_code}|{memory.color_spec}")
        print(f"    确认颜色: {', '.join(memory.confirmed_colors)}")
        print(f"    使用次数: {memory.usage_count}, 置信度: {memory.confidence:.2f}")
        print(f"    创建时间: {memory.created_time[:19]}")
        print()
    
    print("⚙️ 记忆设置:")
    for key, value in manager.settings.items():
        print(f"  • {key}: {value}")
    
    print("\n📤 场景：导出记忆数据")
    print("-" * 40)
    
    export_file = f"memory_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    success = manager.export_memories(export_file)
    
    if success:
        print(f"✅ 记忆数据已导出到: {export_file}")
        
        # 显示导出文件内容
        with open(export_file, 'r', encoding='utf-8') as f:
            export_data = json.load(f)
        
        print(f"📄 导出文件包含:")
        print(f"  • 记忆数量: {len(export_data.get('memories', {}))}")
        print(f"  • 导出时间: {export_data.get('export_time', '')[:19]}")
        print(f"  • 版本信息: {export_data.get('version', '')}")
    else:
        print("❌ 导出失败")
    
    print("\n🧹 清理演示文件")
    print("-" * 40)
    
    # 清理演示文件
    for file_path in [demo_file, export_file]:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"✅ 已删除: {file_path}")
    
    print("\n🎉 智能记忆功能演示完成！")
    print("=" * 60)


def demo_memory_settings():
    """演示记忆设置功能"""
    print("\n⚙️ 智能记忆设置演示")
    print("-" * 40)
    
    manager = SmartMemoryManager("demo_settings.json")
    
    print("📋 默认设置:")
    for key, value in manager.settings.items():
        print(f"  • {key}: {value}")
    
    print("\n🔧 修改设置:")
    new_settings = {
        "enabled": True,
        "auto_save_on_upload": True,
        "auto_confirm_threshold": 0.9,  # 提高自动确认阈值
        "max_memories": 500,            # 降低最大记忆数量
        "backup_enabled": True
    }
    
    success = manager.update_settings(new_settings)
    print(f"✅ 设置更新结果: {success}")
    
    print("\n📋 更新后设置:")
    for key, value in manager.settings.items():
        print(f"  • {key}: {value}")
    
    # 清理
    if os.path.exists("demo_settings.json"):
        os.remove("demo_settings.json")
        print("\n🧹 已清理演示文件")


def demo_memory_management():
    """演示记忆管理功能"""
    print("\n🗂️ 记忆管理功能演示")
    print("-" * 40)
    
    manager = SmartMemoryManager("demo_management.json")
    
    # 添加一些测试记忆
    test_memories = [
        ("TEST001", "红色", ["红色", "深红"]),
        ("TEST002", "蓝色", ["蓝色"]),
        ("TEST003", "绿色", ["绿色", "浅绿"]),
    ]
    
    print("📝 添加测试记忆:")
    for sku, color, colors in test_memories:
        manager.add_temp_memory(sku, color, colors, "TEST", "test.jpg")
        print(f"  ✅ {sku}|{color}")
    
    manager.commit_temp_memories()
    print("💾 已提交所有记忆")
    
    print(f"\n📊 记忆统计:")
    stats = manager.get_memory_statistics()
    print(f"  • 总记忆数量: {stats.get('total_memories', 0)}")
    print(f"  • 总使用次数: {stats.get('total_usage', 0)}")
    
    print(f"\n🗑️ 删除记忆演示:")
    memory_key = manager.generate_memory_key("TEST002", "蓝色")
    success = manager.delete_memory(memory_key)
    print(f"  删除 {memory_key}: {success}")
    
    print(f"\n📊 删除后统计:")
    stats = manager.get_memory_statistics()
    print(f"  • 总记忆数量: {stats.get('total_memories', 0)}")
    
    # 清理
    if os.path.exists("demo_management.json"):
        os.remove("demo_management.json")
        print("\n🧹 已清理演示文件")


if __name__ == "__main__":
    try:
        # 主要工作流程演示
        demo_smart_memory_workflow()
        
        # 设置功能演示
        demo_memory_settings()
        
        # 管理功能演示
        demo_memory_management()
        
        print("\n" + "=" * 60)
        print("🎊 所有演示完成！智能记忆功能运行正常。")
        print("\n💡 使用提示:")
        print("1. 在配置设置中找到'🧠 智能记忆'选项卡")
        print("2. 调整自动确认阈值以适应您的使用习惯")
        print("3. 定期导出记忆数据进行备份")
        print("4. 使用记忆管理功能清理不需要的记忆")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
