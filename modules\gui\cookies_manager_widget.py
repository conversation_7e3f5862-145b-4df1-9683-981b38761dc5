"""
Cookies管理器组件 - 用于管理ERP系统的认证cookies
"""

import json
import os
import re
from datetime import datetime
from typing import Dict, Any, Optional

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *


class CookiesManagerWidget(QWidget):
    """Cookies管理器组件"""
    
    # 信号定义
    cookies_updated = pyqtSignal(dict)  # cookies更新信号
    status_changed = pyqtSignal(str, str)  # 状态变化信号 (status, message)
    
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.current_cookies = {}
        self.last_validation_time = None
        self.validation_status = "unknown"  # unknown, valid, invalid
        
        self.init_ui()
        self.load_current_cookies()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("🍪 ERP Cookies 管理")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 状态指示器
        self.create_status_indicator(layout)
        
        # 使用说明
        self.create_usage_instructions(layout)
        
        # Cookies输入区域
        self.create_input_area(layout)
        
        # 操作按钮组
        self.create_button_group(layout)
        
        # 添加弹性空间
        layout.addStretch()
        
    def create_status_indicator(self, layout):
        """创建状态指示器"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #161617;
                border: 1px solid #3a3a3a;
                border-radius: 12px;
                padding: 10px;
            }
        """)
        
        status_layout = QHBoxLayout(status_frame)
        
        # 状态标签
        self.status_label = QLabel("⚪ 状态: 未知")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #6c757d;
                color: white;
                border-radius: 12px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        # 最后验证时间标签
        self.last_check_label = QLabel("最后检查: 从未")
        self.last_check_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 11px;
                margin-left: 10px;
            }
        """)
        
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.last_check_label)
        status_layout.addStretch()
        
        layout.addWidget(status_frame)
        
    def create_usage_instructions(self, layout):
        """创建使用说明"""
        instructions = QLabel("""
📋 <b>使用说明</b>：
• 支持多种格式：浏览器开发者工具格式、JSON格式、键值对格式
• 点击"🔗 直达ERP系统"按钮打开ERP页面，从浏览器开发者工具复制cookies
• 粘贴到下方文本框，点击"🔍 解析"按钮自动识别格式
• 点击"✅ 验证"测试cookies有效性，"💾 保存"保存到系统
        """)
        instructions.setStyleSheet("""
            QLabel {
                color: #cccccc;
                font-size: 11px;
                background-color: #1a1a1a;
                border: 1px solid #333333;
                border-radius: 8px;
                padding: 12px;
                line-height: 1.4;
            }
        """)
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
    def create_input_area(self, layout):
        """创建输入区域"""
        input_label = QLabel("📝 Cookies 输入区域:")
        input_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-weight: bold;
                font-size: 13px;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(input_label)
        
        # 创建输入文本框
        self.cookies_input = QTextEdit()
        self.cookies_input.setPlaceholderText("""请粘贴cookies，支持以下格式：

1. 浏览器格式：
_sid18707109=ABC123; u_id=18707109; u_co_id=13881863; isLogin=true

2. JSON格式：
{"_sid18707109": "ABC123", "u_id": "18707109", "u_co_id": "13881863"}

3. 键值对格式：
_sid18707109: ABC123
u_id: 18707109
u_co_id: 13881863""")
        
        self.cookies_input.setStyleSheet("""
            QTextEdit {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 8px;
                padding: 12px;
                color: #ffffff;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #0078d4;
            }
        """)
        self.cookies_input.setMinimumHeight(200)
        self.cookies_input.setAcceptDrops(True)
        
        layout.addWidget(self.cookies_input)
        
    def create_button_group(self, layout):
        """创建操作按钮组"""
        button_layout = QHBoxLayout()
        
        # 解析按钮
        self.parse_btn = QPushButton("🔍 解析")
        self.parse_btn.setToolTip("自动识别并解析cookies格式")
        self.parse_btn.clicked.connect(self.parse_cookies)
        
        # 验证按钮
        self.validate_btn = QPushButton("✅ 验证")
        self.validate_btn.setToolTip("测试cookies是否有效")
        self.validate_btn.clicked.connect(self.validate_cookies)
        
        # 保存按钮
        self.save_btn = QPushButton("💾 保存")
        self.save_btn.setToolTip("保存cookies到系统")
        self.save_btn.clicked.connect(self.save_cookies)
        
        # 清空按钮
        self.clear_btn = QPushButton("🗑 清空")
        self.clear_btn.setToolTip("清空输入框和重置状态")
        self.clear_btn.clicked.connect(self.clear_cookies)
        
        # 直达ERP按钮
        self.open_erp_btn = QPushButton("🔗 直达ERP系统")
        self.open_erp_btn.setToolTip("打开ERP系统页面")
        self.open_erp_btn.clicked.connect(self.open_erp_system)
        
        # 设置按钮样式
        button_style = """
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #999999;
            }
        """
        
        # 特殊按钮样式
        clear_style = button_style.replace("#0078d4", "#dc3545").replace("#106ebe", "#c82333").replace("#005a9e", "#bd2130")
        open_erp_style = button_style.replace("#0078d4", "#28a745").replace("#106ebe", "#218838").replace("#005a9e", "#1e7e34")
        
        for btn in [self.parse_btn, self.validate_btn, self.save_btn]:
            btn.setStyleSheet(button_style)
            
        self.clear_btn.setStyleSheet(clear_style)
        self.open_erp_btn.setStyleSheet(open_erp_style)
        
        # 添加按钮到布局
        button_layout.addWidget(self.parse_btn)
        button_layout.addWidget(self.validate_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.open_erp_btn)
        
        layout.addLayout(button_layout)

    def load_current_cookies(self):
        """加载当前cookies"""
        try:
            cookies_file = "latest_cookies.json"
            if os.path.exists(cookies_file):
                with open(cookies_file, 'r', encoding='utf-8') as f:
                    self.current_cookies = json.load(f)
                    self.update_status("unknown", "已加载现有cookies，需要验证")
                    self.main_window.log_message(f"已加载 {len(self.current_cookies)} 个cookies")
            else:
                self.update_status("unknown", "未找到cookies文件")
        except Exception as e:
            self.update_status("invalid", f"加载cookies失败: {str(e)}")
            self.main_window.log_message(f"加载cookies失败: {str(e)}", "ERROR")

    def update_status(self, status: str, message: str = ""):
        """更新状态显示"""
        self.validation_status = status

        if status == "valid":
            self.status_label.setText("🟢 状态: 有效")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #28a745;
                    color: white;
                    border-radius: 12px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 12px;
                }
            """)
            if self.last_validation_time:
                time_str = self.last_validation_time.strftime("%H:%M:%S")
                self.last_check_label.setText(f"最后检查: {time_str}")
        elif status == "invalid":
            self.status_label.setText("🔴 状态: 无效")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #dc3545;
                    color: white;
                    border-radius: 12px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 12px;
                }
            """)
            self.last_check_label.setText(f"错误: {message}")
        else:  # unknown
            self.status_label.setText("⚪ 状态: 未知")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #6c757d;
                    color: white;
                    border-radius: 12px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 12px;
                }
            """)
            self.last_check_label.setText("需要验证")

        # 发送状态变化信号
        self.status_changed.emit(status, message)

    def parse_cookies(self):
        """解析cookies"""
        try:
            text = self.cookies_input.toPlainText().strip()
            if not text:
                self.main_window.log_message("请先输入cookies内容", "WARNING")
                return

            parsed_cookies = self._parse_cookies_text(text)

            if parsed_cookies:
                self.current_cookies = parsed_cookies
                self.main_window.log_message(f"成功解析 {len(parsed_cookies)} 个cookies")

                # 显示解析结果
                result_text = "解析结果:\n"
                for key, value in list(parsed_cookies.items())[:5]:  # 只显示前5个
                    result_text += f"• {key}: {value[:20]}{'...' if len(value) > 20 else ''}\n"
                if len(parsed_cookies) > 5:
                    result_text += f"... 还有 {len(parsed_cookies) - 5} 个cookies"

                QMessageBox.information(self, "解析成功", result_text)
                self.update_status("unknown", "已解析，需要验证")
            else:
                self.main_window.log_message("cookies解析失败，请检查格式", "ERROR")
                QMessageBox.warning(self, "解析失败", "无法识别cookies格式，请检查输入内容")

        except Exception as e:
            self.main_window.log_message(f"解析cookies异常: {str(e)}", "ERROR")
            QMessageBox.critical(self, "解析错误", f"解析过程中发生错误:\n{str(e)}")

    def _parse_cookies_text(self, text: str) -> Dict[str, str]:
        """解析cookies文本，支持多种格式"""
        text = text.strip()

        # 1. 尝试JSON格式
        if text.startswith('{') and text.endswith('}'):
            try:
                cookies_dict = json.loads(text)
                return {str(k): str(v) for k, v in cookies_dict.items()}
            except json.JSONDecodeError:
                pass

        # 2. 尝试浏览器格式（分号分隔）
        if ';' in text:
            cookies = {}
            for item in text.split(';'):
                item = item.strip()
                if '=' in item:
                    key, value = item.split('=', 1)
                    cookies[key.strip()] = value.strip()
            if cookies:
                return cookies

        # 3. 尝试键值对格式（换行分隔）
        if '\n' in text:
            cookies = {}
            for line in text.split('\n'):
                line = line.strip()
                if ':' in line:
                    key, value = line.split(':', 1)
                    cookies[key.strip()] = value.strip()
                elif '=' in line:
                    key, value = line.split('=', 1)
                    cookies[key.strip()] = value.strip()
            if cookies:
                return cookies

        # 4. 单个cookie
        if '=' in text:
            key, value = text.split('=', 1)
            return {key.strip(): value.strip()}

        return {}

    def validate_cookies(self):
        """验证cookies有效性"""
        if not self.current_cookies:
            self.main_window.log_message("请先解析cookies", "WARNING")
            QMessageBox.warning(self, "验证失败", "请先输入并解析cookies")
            return

        try:
            # 禁用验证按钮，显示进度
            self.validate_btn.setEnabled(False)
            self.validate_btn.setText("🔄 验证中...")

            # 创建临时ERP集成器进行验证
            from modules.erp_integration import ERPIntegration
            temp_erp = ERPIntegration(log_callback=self.main_window.log_message)

            # 更新cookies
            temp_erp.cookies.update(self.current_cookies)

            # 执行验证
            self.main_window.log_message("开始验证cookies有效性...")
            is_valid = temp_erp.check_auth_status()

            self.last_validation_time = datetime.now()

            if is_valid:
                self.update_status("valid", "验证成功")
                self.main_window.log_message("✅ Cookies验证成功")
                QMessageBox.information(self, "验证成功", "Cookies有效，可以正常使用ERP功能")
            else:
                self.update_status("invalid", "认证失败")
                self.main_window.log_message("❌ Cookies验证失败", "ERROR")
                QMessageBox.warning(self, "验证失败", "Cookies无效或已过期，请重新获取")

        except Exception as e:
            self.update_status("invalid", f"验证异常: {str(e)}")
            self.main_window.log_message(f"验证cookies异常: {str(e)}", "ERROR")
            QMessageBox.critical(self, "验证错误", f"验证过程中发生错误:\n{str(e)}")

        finally:
            # 恢复验证按钮
            self.validate_btn.setEnabled(True)
            self.validate_btn.setText("✅ 验证")

    def save_cookies(self):
        """保存cookies到文件"""
        if not self.current_cookies:
            self.main_window.log_message("没有可保存的cookies", "WARNING")
            QMessageBox.warning(self, "保存失败", "请先输入并解析cookies")
            return

        try:
            # 询问用户是否确认保存
            reply = QMessageBox.question(
                self,
                "确认保存",
                f"确定要保存 {len(self.current_cookies)} 个cookies到系统吗？\n\n这将覆盖现有的cookies配置。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 保存到文件
                cookies_file = "latest_cookies.json"
                with open(cookies_file, 'w', encoding='utf-8') as f:
                    json.dump(self.current_cookies, f, ensure_ascii=False, indent=2)

                self.main_window.log_message(f"✅ 已保存 {len(self.current_cookies)} 个cookies到 {cookies_file}")

                # 发送cookies更新信号
                self.cookies_updated.emit(self.current_cookies)

                # 更新主程序的ERP集成器
                if hasattr(self.main_window, 'erp_integration'):
                    self.main_window.erp_integration.cookies.update(self.current_cookies)
                    self.main_window.erp_integration.auth_valid = False  # 重置认证状态
                    self.main_window.log_message("已更新ERP集成器的cookies")

                QMessageBox.information(self, "保存成功", f"已成功保存 {len(self.current_cookies)} 个cookies")

        except Exception as e:
            self.main_window.log_message(f"保存cookies失败: {str(e)}", "ERROR")
            QMessageBox.critical(self, "保存错误", f"保存过程中发生错误:\n{str(e)}")

    def clear_cookies(self):
        """清空cookies和重置状态"""
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空所有cookies输入和重置状态吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.cookies_input.clear()
            self.current_cookies = {}
            self.last_validation_time = None
            self.update_status("unknown", "已清空")
            self.main_window.log_message("已清空cookies输入")

    def open_erp_system(self):
        """打开ERP系统页面"""
        try:
            import webbrowser
            erp_url = "https://src.erp321.com/erp-web-group/erp-scm-goods/goodsInventoryManagement?tabAllow=camera&_c=jst-epaas"
            webbrowser.open(erp_url)
            self.main_window.log_message("已打开ERP系统页面")

            # 显示操作提示
            QMessageBox.information(
                self,
                "ERP系统已打开",
                "ERP系统页面已在浏览器中打开。\n\n"
                "请按以下步骤获取cookies：\n"
                "1. 在ERP页面登录您的账户\n"
                "2. 按F12打开开发者工具\n"
                "3. 切换到Network(网络)标签\n"
                "4. 刷新页面或进行任意操作\n"
                "5. 找到任意请求，复制Request Headers中的Cookie值\n"
                "6. 粘贴到下方输入框中"
            )

        except Exception as e:
            self.main_window.log_message(f"打开ERP系统失败: {str(e)}", "ERROR")
            QMessageBox.critical(self, "打开失败", f"无法打开ERP系统:\n{str(e)}")

    def get_current_status(self) -> tuple:
        """获取当前状态"""
        return (self.validation_status, len(self.current_cookies), self.last_validation_time)
