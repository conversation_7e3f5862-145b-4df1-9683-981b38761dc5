"""
Cookies验证器模块 - 负责验证cookies的格式和有效性
"""

import re
from typing import Dict, List, Tuple, Optional
from .exceptions import CookiesValidationError


class CookiesValidator:
    """Cookies验证器 - 验证cookies格式和有效性"""
    
    def __init__(self):
        # ERP系统必需的cookie字段
        self.required_cookies = [
            'u_id',        # 用户ID
            'u_co_id',     # 公司ID
        ]
        
        # 重要的cookie字段（建议包含）
        self.important_cookies = [
            '_sid',        # 会话ID（可能有数字后缀）
            'isLogin',     # 登录状态
            'u_sso_token', # SSO令牌
        ]
    
    def validate_format(self, cookies: Dict[str, str]) -> Tuple[bool, List[str]]:
        """
        验证cookies格式
        
        Args:
            cookies: 待验证的cookies字典
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        if not cookies:
            errors.append("cookies不能为空")
            return False, errors
        
        # 检查cookie名称格式
        for key, value in cookies.items():
            if not self._is_valid_cookie_name(key):
                errors.append(f"无效的cookie名称: {key}")
            
            if not self._is_valid_cookie_value(value):
                errors.append(f"无效的cookie值: {key}={value}")
        
        # 检查必需字段
        missing_required = []
        for required in self.required_cookies:
            if not any(key == required or key.startswith(required) for key in cookies.keys()):
                missing_required.append(required)
        
        if missing_required:
            errors.append(f"缺少必需的cookie字段: {', '.join(missing_required)}")
        
        # 检查重要字段
        missing_important = []
        for important in self.important_cookies:
            if not any(key == important or key.startswith(important.split('_')[0]) for key in cookies.keys()):
                missing_important.append(important)
        
        if missing_important:
            errors.append(f"建议包含以下cookie字段: {', '.join(missing_important)}")
        
        return len(errors) == 0, errors
    
    def validate_with_erp(self, cookies: Dict[str, str], erp_integration) -> Tuple[bool, str]:
        """
        通过ERP系统验证cookies有效性
        
        Args:
            cookies: 待验证的cookies字典
            erp_integration: ERP集成器实例
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 备份原有cookies
            original_cookies = erp_integration.cookies.copy()
            
            # 更新cookies
            erp_integration.cookies.clear()
            erp_integration.cookies.update(cookies)
            
            # 执行认证检查
            is_valid = erp_integration.check_auth_status()
            
            # 恢复原有cookies
            erp_integration.cookies.clear()
            erp_integration.cookies.update(original_cookies)
            
            if is_valid:
                return True, "ERP认证验证成功"
            else:
                return False, "ERP认证验证失败，cookies可能已过期或无效"
                
        except Exception as e:
            # 恢复原有cookies
            try:
                erp_integration.cookies.clear()
                erp_integration.cookies.update(original_cookies)
            except:
                pass
            
            return False, f"ERP验证过程中发生错误: {str(e)}"
    
    def _is_valid_cookie_name(self, name: str) -> bool:
        """验证cookie名称是否有效"""
        if not name or not isinstance(name, str):
            return False
        
        # Cookie名称不能包含特殊字符
        invalid_chars = [' ', '\t', '\n', '\r', '=', ';', ',']
        for char in invalid_chars:
            if char in name:
                return False
        
        return True
    
    def _is_valid_cookie_value(self, value: str) -> bool:
        """验证cookie值是否有效"""
        if not isinstance(value, str):
            return False
        
        # Cookie值不能包含某些特殊字符
        invalid_chars = ['\n', '\r']
        for char in invalid_chars:
            if char in value:
                return False
        
        return True
    
    def get_cookies_info(self, cookies: Dict[str, str]) -> Dict[str, any]:
        """
        获取cookies的详细信息
        
        Args:
            cookies: cookies字典
            
        Returns:
            cookies信息字典
        """
        info = {
            'total_count': len(cookies),
            'required_present': [],
            'required_missing': [],
            'important_present': [],
            'important_missing': [],
            'other_cookies': [],
            'potential_issues': []
        }
        
        # 检查必需字段
        for required in self.required_cookies:
            found = False
            for key in cookies.keys():
                if key == required or key.startswith(required):
                    info['required_present'].append(key)
                    found = True
                    break
            if not found:
                info['required_missing'].append(required)
        
        # 检查重要字段
        for important in self.important_cookies:
            found = False
            for key in cookies.keys():
                if key == important or key.startswith(important.split('_')[0]):
                    info['important_present'].append(key)
                    found = True
                    break
            if not found:
                info['important_missing'].append(important)
        
        # 其他cookies
        all_known = set(info['required_present'] + info['important_present'])
        for key in cookies.keys():
            if key not in all_known:
                info['other_cookies'].append(key)
        
        # 检查潜在问题
        for key, value in cookies.items():
            if not value:
                info['potential_issues'].append(f"Cookie '{key}' 的值为空")
            elif len(value) < 5:
                info['potential_issues'].append(f"Cookie '{key}' 的值可能过短")
        
        return info
    
    def suggest_fixes(self, cookies: Dict[str, str]) -> List[str]:
        """
        为cookies问题提供修复建议
        
        Args:
            cookies: cookies字典
            
        Returns:
            修复建议列表
        """
        suggestions = []
        
        # 检查格式问题
        is_valid, errors = self.validate_format(cookies)
        if not is_valid:
            suggestions.append("请检查cookies格式是否正确")
            suggestions.extend([f"• {error}" for error in errors])
        
        # 检查必需字段
        info = self.get_cookies_info(cookies)
        if info['required_missing']:
            suggestions.append("缺少必需的cookie字段，请确保：")
            suggestions.append("• 已在ERP系统中正确登录")
            suggestions.append("• 从正确的ERP页面复制cookies")
            suggestions.append("• cookies没有被截断或遗漏")
        
        if info['important_missing']:
            suggestions.append("建议包含以下重要字段以确保功能完整：")
            suggestions.extend([f"• {field}" for field in info['important_missing']])
        
        if info['potential_issues']:
            suggestions.append("发现以下潜在问题：")
            suggestions.extend([f"• {issue}" for issue in info['potential_issues']])
        
        if not suggestions:
            suggestions.append("cookies格式看起来正常，如果仍有问题请尝试重新获取")
        
        return suggestions
