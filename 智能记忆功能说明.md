# 🧠 智能记忆功能使用说明

## 📋 功能概述

智能记忆功能是为待确认商品卡片设计的自动化记忆系统，能够记住用户的颜色确认选择，减少重复性操作，提高工作效率。

## 🌟 核心特性

### 1. 自动记忆学习
- **智能记录**：系统自动记录用户在颜色确认面板中的选择
- **精确匹配**：基于款号+颜色规格的精确匹配机制
- **置信度评估**：根据使用频率自动调整记忆的可信度

### 2. 自动确认功能
- **智能识别**：加载商品时自动检查是否存在匹配的历史记忆
- **自动应用**：置信度达到阈值时自动应用历史选择
- **手动修正**：用户仍可手动修改自动确认的结果

### 3. 数据管理
- **临时存储**：确认时先存储到内存，给用户修改机会
- **上传提交**：只有在用户执行上传操作时才永久保存
- **统计分析**：提供详细的使用统计和记忆分析

## 🚀 使用流程

### 第一步：正常颜色确认
1. 在颜色确认面板中选择正确的颜色规格
2. 点击"确认选择"按钮
3. 系统自动将选择添加到临时记忆

### 第二步：上传时保存记忆
1. 完成所有颜色确认后，点击"💰 上传成本"
2. 系统在上传成功后自动将临时记忆保存到永久存储
3. 显示"智能记忆已保存"提示信息

### 第三步：自动确认体验
1. 下次处理相同款号+颜色规格的商品时
2. 系统自动检查历史记忆
3. 如果找到匹配且置信度足够，自动应用历史选择
4. 显示"🧠 智能记忆自动确认"提示

## ⚙️ 配置管理

### 访问配置界面
1. 点击"⚙️ 配置设置"标签页
2. 在左侧列表中选择"🧠 智能记忆"
3. 进入智能记忆管理界面

### 基本设置
- **启用智能记忆**：控制整个功能的开关
- **上传时自动保存**：控制是否在上传时自动保存记忆
- **自动确认阈值**：设置自动确认的置信度要求（默认80%）
- **最大记忆数量**：限制系统保存的记忆总数（默认1000条）

### 记忆管理
- **查看记忆列表**：显示所有已保存的记忆记录
- **查看详情**：点击"👁️ 查看详情"查看记忆的完整信息
- **删除记忆**：选择记忆后点击"🗑️ 删除记忆"
- **清空所有**：点击"🧹 清空所有"清除所有记忆数据

### 导入导出
- **导出记忆**：将记忆数据导出为JSON文件备份
- **导入记忆**：从JSON文件导入记忆数据
- **合并模式**：导入时可选择合并或替换现有记忆

## 📊 统计信息

### 基本统计
- **总记忆数量**：系统中保存的记忆总数
- **总使用次数**：所有记忆的累计使用次数
- **临时记忆**：当前待提交的临时记忆数量

### 使用分析
- **最常用记忆**：显示使用频率最高的记忆
- **最近使用**：显示最近使用的记忆记录
- **置信度分布**：记忆的可信度统计

## 🔧 技术实现

### 数据结构
```json
{
  "sku_code": "款号",
  "color_spec": "颜色规格", 
  "confirmed_colors": ["用户确认的颜色列表"],
  "supplier": "供应商",
  "file_pattern": "文件名模式",
  "created_time": "创建时间",
  "last_used": "最后使用时间",
  "usage_count": "使用次数",
  "confidence": "置信度",
  "notes": "备注信息"
}
```

### 匹配算法
- **精确匹配**：款号+颜色规格完全一致
- **置信度评估**：基于使用频率和成功率
- **自动学习**：每次使用后提升置信度

### 存储机制
- **文件位置**：`data/smart_memory.json`
- **备份策略**：每次保存前自动备份
- **版本控制**：支持数据格式版本管理

## 🛡️ 安全特性

### 数据保护
- **本地存储**：所有记忆数据保存在本地
- **自动备份**：重要操作前自动创建备份
- **错误恢复**：支持从备份文件恢复数据

### 隐私保护
- **用户控制**：用户完全控制记忆的保存和删除
- **透明操作**：所有记忆操作都有明确提示
- **数据清理**：支持一键清除所有记忆数据

## 🔍 故障排除

### 常见问题

#### 记忆未自动确认
- 检查"启用智能记忆"是否开启
- 确认置信度是否达到自动确认阈值
- 验证款号和颜色规格是否完全匹配

#### 记忆未保存
- 确认"上传时自动保存"是否开启
- 检查是否完成了上传操作
- 查看是否有错误提示信息

#### 配置界面无法访问
- 重启程序重新初始化
- 检查智能记忆管理器是否正确加载
- 查看控制台是否有错误信息

### 数据恢复
1. 查找备份文件：`data/smart_memory.json.backup`
2. 将备份文件重命名为：`data/smart_memory.json`
3. 重启程序加载恢复的数据

## 📈 性能优化

### 内存管理
- **延迟保存**：临时记忆仅在内存中存储
- **批量提交**：上传时一次性提交所有临时记忆
- **自动清理**：定期清理过期的临时记忆

### 查询优化
- **索引机制**：基于款号+颜色规格的快速索引
- **缓存策略**：常用记忆保持在内存缓存中
- **异步处理**：记忆操作不阻塞主界面

## 🎯 最佳实践

### 使用建议
1. **保持一致性**：对相同商品使用一致的颜色选择
2. **及时确认**：确认颜色后尽快上传以保存记忆
3. **定期备份**：定期导出记忆数据进行备份
4. **合理设置**：根据使用习惯调整自动确认阈值

### 维护建议
1. **定期清理**：删除不再需要的过期记忆
2. **数据检查**：定期检查记忆数据的准确性
3. **版本更新**：及时更新到最新版本获得更好体验

---

**智能记忆功能** - 让重复工作变得简单高效！ 🎉
