#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能记忆管理器 - 管理颜色确认的智能记忆功能
负责记忆数据的存储、检索、匹配和管理
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import shutil


@dataclass
class MemoryRecord:
    """记忆记录数据结构"""
    sku_code: str                    # 款号
    color_spec: str                  # 颜色规格
    confirmed_colors: List[str]      # 用户确认的颜色选择
    supplier: str                    # 供应商
    file_pattern: str               # 文件名模式
    created_time: str               # 创建时间
    last_used: str                  # 最后使用时间
    usage_count: int                # 使用次数
    confidence: float               # 置信度
    notes: str                      # 备注


class SmartMemoryManager:
    """智能记忆管理器"""
    
    def __init__(self, memory_file: str = "data/smart_memory.json"):
        self.memory_file = memory_file
        self.memories: Dict[str, MemoryRecord] = {}
        self.settings = {
            "enabled": True,
            "auto_save_on_upload": True,
            "auto_confirm_threshold": 0.8,  # 自动确认的置信度阈值
            "max_memories": 1000,
            "backup_enabled": True
        }
        self.temp_memories: Dict[str, MemoryRecord] = {}  # 临时记忆（上传前暂存）
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.memory_file), exist_ok=True)
        self.load_memories()
    
    def generate_memory_key(self, sku_code: str, color_spec: str) -> str:
        """生成记忆键值"""
        return f"{sku_code}|{color_spec}"
    
    def load_memories(self) -> bool:
        """加载记忆数据"""
        try:
            if not os.path.exists(self.memory_file):
                self.save_memories()  # 创建默认文件
                return True
            
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载设置
            if 'settings' in data:
                self.settings.update(data['settings'])
            
            # 加载记忆数据
            if 'memories' in data:
                for key, memory_data in data['memories'].items():
                    self.memories[key] = MemoryRecord(**memory_data)
            
            return True
            
        except Exception as e:
            print(f"❌ 加载智能记忆失败: {str(e)}")
            return False
    
    def save_memories(self) -> bool:
        """保存记忆数据"""
        try:
            # 备份现有文件
            if self.settings.get("backup_enabled", True) and os.path.exists(self.memory_file):
                backup_file = f"{self.memory_file}.backup"
                shutil.copy2(self.memory_file, backup_file)
            
            # 准备保存数据
            save_data = {
                "version": "1.0",
                "last_updated": datetime.now().isoformat(),
                "settings": self.settings,
                "memories": {}
            }
            
            # 转换记忆数据
            for key, memory in self.memories.items():
                save_data["memories"][key] = asdict(memory)
            
            # 保存到文件
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"❌ 保存智能记忆失败: {str(e)}")
            return False
    
    def add_temp_memory(self, sku_code: str, color_spec: str, confirmed_colors: List[str],
                       supplier: str = "", file_pattern: str = "", notes: str = "") -> str:
        """添加临时记忆（上传前暂存）"""
        try:
            memory_key = self.generate_memory_key(sku_code, color_spec)
            
            memory = MemoryRecord(
                sku_code=sku_code,
                color_spec=color_spec,
                confirmed_colors=confirmed_colors.copy(),
                supplier=supplier,
                file_pattern=file_pattern,
                created_time=datetime.now().isoformat(),
                last_used=datetime.now().isoformat(),
                usage_count=1,
                confidence=1.0,
                notes=notes
            )
            
            self.temp_memories[memory_key] = memory
            print(f"✅ 临时记忆已添加: {memory_key}")
            return memory_key
            
        except Exception as e:
            print(f"❌ 添加临时记忆失败: {str(e)}")
            return ""
    
    def commit_temp_memories(self) -> bool:
        """提交临时记忆到永久存储（上传时调用）"""
        try:
            if not self.settings.get("auto_save_on_upload", True):
                return True
            
            committed_count = 0
            for key, temp_memory in self.temp_memories.items():
                if key in self.memories:
                    # 更新现有记忆
                    existing = self.memories[key]
                    existing.confirmed_colors = temp_memory.confirmed_colors
                    existing.last_used = temp_memory.last_used
                    existing.usage_count += 1
                    existing.confidence = min(1.0, existing.confidence + 0.1)
                else:
                    # 添加新记忆
                    self.memories[key] = temp_memory
                
                committed_count += 1
            
            # 清空临时记忆
            self.temp_memories.clear()
            
            # 保存到文件
            if committed_count > 0:
                self.save_memories()
                print(f"✅ 已提交 {committed_count} 条记忆到永久存储")
            
            return True
            
        except Exception as e:
            print(f"❌ 提交临时记忆失败: {str(e)}")
            return False
    
    def find_matching_memory(self, sku_code: str, color_spec: str) -> Optional[MemoryRecord]:
        """查找匹配的记忆"""
        try:
            if not self.settings.get("enabled", True):
                return None
            
            memory_key = self.generate_memory_key(sku_code, color_spec)
            
            # 精确匹配
            if memory_key in self.memories:
                memory = self.memories[memory_key]
                if memory.confidence >= self.settings.get("auto_confirm_threshold", 0.8):
                    return memory
            
            # 模糊匹配（可选实现）
            # TODO: 可以添加基于编辑距离的模糊匹配逻辑
            
            return None
            
        except Exception as e:
            print(f"❌ 查找匹配记忆失败: {str(e)}")
            return None
    
    def update_memory_usage(self, sku_code: str, color_spec: str) -> bool:
        """更新记忆使用统计"""
        try:
            memory_key = self.generate_memory_key(sku_code, color_spec)
            
            if memory_key in self.memories:
                memory = self.memories[memory_key]
                memory.last_used = datetime.now().isoformat()
                memory.usage_count += 1
                memory.confidence = min(1.0, memory.confidence + 0.05)
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ 更新记忆使用统计失败: {str(e)}")
            return False
    
    def delete_memory(self, memory_key: str) -> bool:
        """删除指定记忆"""
        try:
            if memory_key in self.memories:
                del self.memories[memory_key]
                self.save_memories()
                return True
            return False
            
        except Exception as e:
            print(f"❌ 删除记忆失败: {str(e)}")
            return False
    
    def clear_all_memories(self) -> bool:
        """清空所有记忆"""
        try:
            self.memories.clear()
            self.temp_memories.clear()
            self.save_memories()
            return True
            
        except Exception as e:
            print(f"❌ 清空记忆失败: {str(e)}")
            return False
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        try:
            total_memories = len(self.memories)
            total_usage = sum(memory.usage_count for memory in self.memories.values())
            
            # 按使用次数排序
            most_used = sorted(
                self.memories.items(),
                key=lambda x: x[1].usage_count,
                reverse=True
            )[:5]
            
            # 最近使用的记忆
            recent_used = sorted(
                self.memories.items(),
                key=lambda x: x[1].last_used,
                reverse=True
            )[:5]
            
            return {
                "total_memories": total_memories,
                "total_usage": total_usage,
                "temp_memories": len(self.temp_memories),
                "most_used": [(key, memory.usage_count) for key, memory in most_used],
                "recent_used": [(key, memory.last_used) for key, memory in recent_used],
                "settings": self.settings.copy()
            }
            
        except Exception as e:
            print(f"❌ 获取记忆统计失败: {str(e)}")
            return {}
    
    def export_memories(self, export_file: str) -> bool:
        """导出记忆数据"""
        try:
            export_data = {
                "export_time": datetime.now().isoformat(),
                "version": "1.0",
                "settings": self.settings,
                "memories": {}
            }
            
            for key, memory in self.memories.items():
                export_data["memories"][key] = asdict(memory)
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"❌ 导出记忆失败: {str(e)}")
            return False
    
    def import_memories(self, import_file: str, merge: bool = True) -> bool:
        """导入记忆数据"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if not merge:
                self.memories.clear()
            
            # 导入记忆数据
            if 'memories' in import_data:
                for key, memory_data in import_data['memories'].items():
                    self.memories[key] = MemoryRecord(**memory_data)
            
            # 导入设置（可选）
            if 'settings' in import_data:
                for key, value in import_data['settings'].items():
                    if key in self.settings:
                        self.settings[key] = value
            
            self.save_memories()
            return True
            
        except Exception as e:
            print(f"❌ 导入记忆失败: {str(e)}")
            return False
    
    def update_settings(self, new_settings: Dict[str, Any]) -> bool:
        """更新设置"""
        try:
            self.settings.update(new_settings)
            self.save_memories()
            return True
            
        except Exception as e:
            print(f"❌ 更新设置失败: {str(e)}")
            return False
