"""
事件处理器 - 负责用户交互事件处理
重构自原始pyqt5_main_gui.py的事件处理部分
"""

import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from modules.image_splitter import ImageInfo


class EventHandlers:
    """事件处理器"""
    
    def __init__(self, main_window):
        self.main_window = main_window

    # 文件操作方法（从main_gui_controller移过来）
    def select_files(self):
        """选择文件"""
        try:
            file_types = "图像文件 (*.jpg *.jpeg *.png *.gif *.bmp *.webp *.tiff);;PDF文件 (*.pdf);;所有文件 (*.*)"
            files, _ = QFileDialog.getOpenFileNames(self.main_window, "选择票据文件", "", file_types)
            self.on_file_selected(files)
        except Exception as e:
            self.main_window.log_message(f"选择文件失败: {str(e)}", "ERROR")

    def paste_image(self):
        """粘贴图像"""
        try:
            # 获取剪贴板
            clipboard = QApplication.clipboard()
            mime_data = clipboard.mimeData()

            # 检查剪贴板是否包含图像
            if mime_data.hasImage():
                # 获取图像数据
                image = clipboard.image()
                if image.isNull():
                    self.main_window.log_message("剪贴板中的图像数据无效", "WARNING")
                    return

                # 创建临时文件保存图像
                import tempfile
                import time
                temp_dir = tempfile.gettempdir()
                timestamp = int(time.time())
                temp_filename = f"pasted_image_{timestamp}.png"
                temp_path = os.path.join(temp_dir, temp_filename)

                # 保存图像到临时文件
                if image.save(temp_path, "PNG"):
                    # 添加到文件管理器
                    self.main_window.file_manager.add_files([temp_path])
                    self.main_window.image_manager.update_main_thumbnail_preview()

                    # 分析上传的图像
                    QTimer.singleShot(100, lambda: self.main_window.image_manager.analyze_uploaded_image(temp_path))

                    self.main_window.log_message(f"已粘贴图像: {temp_filename}")
                else:
                    self.main_window.log_message("保存粘贴的图像失败", "ERROR")

            elif mime_data.hasUrls():
                # 检查是否为图像文件URL
                urls = mime_data.urls()
                image_files = []
                for url in urls:
                    file_path = url.toLocalFile()
                    if file_path and file_path.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff')):
                        image_files.append(file_path)

                if image_files:
                    self.on_file_selected(image_files)
                else:
                    self.main_window.log_message("剪贴板中没有有效的图像文件", "WARNING")
            else:
                self.main_window.log_message("剪贴板中没有图像数据", "WARNING")

        except Exception as e:
            self.main_window.log_message(f"粘贴图像失败: {str(e)}", "ERROR")

    def split_selected_image(self):
        """分割选定图像"""
        # 获取当前选中的图像
        if not hasattr(self.main_window.image_manager, 'current_image_path') or not self.main_window.image_manager.current_image_path:
            self.main_window.log_message("请先选择要分割的图像", "WARNING")
            return
            
        file_path = self.main_window.image_manager.current_image_path
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.main_window.log_message("图像文件不存在", "ERROR")
                return
            
            # 提取供应商代码用于分割
            supplier_code = self.main_window.image_manager.extract_supplier_code(
                os.path.splitext(os.path.basename(file_path))[0]
            )
            
            if not supplier_code:
                supplier_code = "UNKNOWN"
            
            # 使用ImageSplitter进行分割
            from modules.image_splitter import ImageSplitter
            splitter = ImageSplitter()
            
            # 分析图像
            image_info = splitter.analyze_image(file_path, supplier_code)
            
            if not image_info.needs_split:
                # 降级为DEBUG级别，这是内部判断信息
                self.main_window.log_message(f"图像尺寸不需要分割 (高度: {image_info.height}px)", "DEBUG")
                return
            
            # 执行分割
            segments = splitter.split_image(file_path, supplier_code)
            
            if segments:
                self.main_window.log_message(f"成功分割图像为 {len(segments)} 个片段")
                
                # 将分割后的图像添加到文件管理器
                segment_paths = [segment.file_path for segment in segments]
                self.main_window.file_manager.add_files(segment_paths)
                
                # 更新缩略图预览
                self.main_window.image_manager.update_main_thumbnail_preview()
                
            else:
                self.main_window.log_message("图像分割失败", "ERROR")
                
        except Exception as e:
            self.main_window.log_message(f"分割图像时发生错误: {str(e)}", "ERROR")
        
    # 文件操作相关事件
    def on_file_selected(self, file_paths):
        """文件选择事件"""
        try:
            if file_paths:
                self.main_window.file_manager.add_files(file_paths)
                self.main_window.image_manager.update_main_thumbnail_preview()
                
                # 异步分析最后一个图像文件，避免阻塞UI
                for file_path in reversed(file_paths):
                    if file_path.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff')):
                        # 使用QTimer延迟执行图像分析，避免阻塞
                        QTimer.singleShot(100, lambda fp=file_path: self.main_window.image_manager.analyze_uploaded_image(fp))
                        break
                
                # 降级为DEBUG级别，用户在界面上已能看到文件添加
                self.main_window.log_message(f"已添加 {len(file_paths)} 个文件", "DEBUG")
            else:
                self.main_window.log_message("未选择任何文件", "INFO")
                
        except Exception as e:
            self.main_window.log_message(f"处理文件选择失败: {str(e)}", "ERROR")
    
    def on_file_cleared(self):
        """文件清除事件"""
        try:
            self.main_window.file_manager.clear_all_files()
            self.main_window.image_manager.clear_main_thumbnails()
            self.main_window.table_ui_manager.clear_all_tables()
            
            # 清空AI响应
            if hasattr(self.main_window, 'ai_response_text'):
                self.main_window.ai_response_text.clear()
            
            self.main_window.log_message("已清除所有文件和数据")
            
        except Exception as e:
            self.main_window.log_message(f"清除文件失败: {str(e)}", "ERROR")
    
    # AI处理相关事件
    def on_ai_processing_started(self):
        """AI处理开始事件"""
        self.main_window.processing = True
        
        # 禁用相关按钮
        if hasattr(self.main_window, 'ai_process_btn'):
            self.main_window.ai_process_btn.setEnabled(False)
        
        # 显示进度条
        if hasattr(self.main_window, 'progress_bar'):
            self.main_window.progress_bar.setVisible(True)
            self.main_window.progress_bar.setValue(0)
        
        # 更新状态
        if hasattr(self.main_window, 'status_bar'):
            self.main_window.status_bar.showMessage("正在处理...")
        
        self.main_window.log_message("开始AI处理")
    
    def on_ai_progress_updated(self, current, total):
        """AI处理进度更新事件"""
        message = f"AI处理进度: {current}/{total}"
        self.main_window.status_bar.showMessage(message)
        self.main_window.log_message(message)
        # 删除对进度条的引用，简化界面
    
    def on_ai_processing_finished(self):
        """AI处理完成事件"""
        # 降级为DEBUG级别，用户在界面上已能看到处理完成状态
        self.main_window.log_message("AI处理完成", "DEBUG")
        self.main_window.status_bar.showMessage("AI处理完成")
        # 删除对进度条的引用，简化界面
    
    def on_ai_result_received(self, file_path, result):
        """AI结果接收事件"""
        try:
            # 只添加到解析结果列表，不自动添加到商品明细表格
            # 避免不同供应商数据自动合并
            self.main_window.table_ui_manager.add_file_result_to_table(file_path, result)
            
            # 显示AI响应
            if hasattr(self.main_window, 'ai_response_text') and result:
                response_text = f"文件: {file_path}\n"
                response_text += f"结果: {str(result)}\n"
                response_text += "-" * 50 + "\n"
                self.main_window.ai_response_text.append(response_text)
            
            # 降级为DEBUG级别，用户在界面上已能看到处理结果
            self.main_window.log_message(f"已处理文件: {file_path}", "DEBUG")
            
        except Exception as e:
            self.main_window.log_message(f"处理AI结果失败: {str(e)}", "ERROR")
    
    # 表格相关事件
    def on_table_row_selected(self, table, row):
        """表格行选择事件"""
        try:
            # 获取选中行的数据
            if table.item(row, 1):  # 款号列
                sku_code = table.item(row, 1).text()
                # 降级为DEBUG级别，用户在界面上已能看到选中状态
                self.main_window.log_message(f"选中商品: {sku_code}", "DEBUG")
                
                # 可以在这里触发颜色确认面板的更新
                
        except Exception as e:
            self.main_window.log_message(f"处理表格选择失败: {str(e)}", "ERROR")
    
    # 图像相关事件
    def on_image_selected(self, file_path):
        """图像选择事件"""
        try:
            self.main_window.image_manager.current_image_path = file_path
            
            # 启用相关按钮
            if hasattr(self.main_window, 'ai_process_btn'):
                self.main_window.ai_process_btn.setEnabled(True)
            
            # 降级为DEBUG级别，用户在界面上已能看到选择状态
            self.main_window.log_message(f"选择图像: {file_path}", "DEBUG")
            
        except Exception as e:
            self.main_window.log_message(f"处理图像选择失败: {str(e)}", "ERROR")
    
    # 配置相关事件
    def on_config_changed(self):
        """配置变更事件"""
        self.main_window.log_message("配置已变更", "DEBUG")

    def on_config_saved(self):
        """配置保存事件"""
        self.main_window.log_message("配置已保存", "DEBUG")
    
    # ERP相关事件
    def on_erp_query_started(self, table_type):
        """ERP查询开始事件"""
        self.main_window.log_message(f"开始查询{table_type}表格的ERP信息")
    
    def on_erp_query_completed(self, table_type, success_count, total_count):
        """ERP查询完成事件"""
        self.main_window.log_message(f"{table_type}表格ERP查询完成: {success_count}/{total_count}")
    
    def on_cost_update_started(self, total):
        """成本更新开始处理"""
        self.main_window.log_message(f"开始更新成本价，共{total}个商品")
        # 删除对进度条的引用，简化界面
    
    def on_cost_update_completed(self, table_type, success_count, total_count):
        """成本价更新完成事件"""
        # 降级为DEBUG级别，这是操作统计信息
        self.main_window.log_message(f"{table_type}表格成本价更新完成: {success_count}/{total_count}", "DEBUG")
    
    # 通用错误处理
    def on_error_occurred(self, error_message, error_type="ERROR"):
        """错误发生事件"""
        self.main_window.log_message(f"发生错误: {error_message}", error_type)
        
        # 重置处理状态
        if self.main_window.processing:
            self.on_ai_processing_finished()
        
        # 可以在这里添加错误对话框显示
        # QMessageBox.critical(self.main_window, "错误", error_message) 