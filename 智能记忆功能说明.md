# 🧠 智能记忆功能说明

## 功能概述

智能记忆功能是为待确认商品卡片设计的自动化记忆系统，能够记住用户的颜色选择偏好，减少重复性操作，提高工作效率。

## 核心功能

### 1. 自动记忆用户选择
- **记忆内容**：款号、颜色规格、用户确认的颜色选择
- **记忆时机**：用户在颜色确认面板中完成颜色选择确认后
- **存储方式**：先存储到临时记忆，上传时提交到永久存储
- **双重记忆机制**：同时生成简单款号和完整款号的记忆，适应不同查询场景

### 2. 智能自动确认
- **匹配条件**：款号 + 颜色规格完全一致（避免不同款号但颜色规格相同的冲突）
- **智能匹配策略**：
  - 简单款号优先匹配完整款号记忆（避免多供应商冲突）
  - 完整款号可以匹配简单款号记忆
  - 多重匹配时选择置信度最高的记忆
- **自动操作**：找到匹配记忆时，自动选择之前确认的颜色，跳过手动确认
- **置信度控制**：只有置信度达到阈值（默认0.8）的记忆才会自动确认

### 3. 记忆数据管理
- **查看记忆**：在配置设置的"智能记忆"选项卡中查看所有记忆
- **删除记忆**：支持删除单条记忆或清空所有记忆
- **导入导出**：支持记忆数据的导入和导出，便于备份和迁移
- **统计信息**：显示总记忆数、使用次数等统计信息

## 使用流程

### 第一次使用
1. 用户在颜色确认面板中选择颜色
2. 点击"确认选择"按钮
3. 系统分析用户输入款号和实际匹配款号
4. 生成双重记忆：
   - 用户输入款号记忆（如"7922|两件套装"）
   - 完整款号记忆（如"DSD106-7922|两件套装"）
5. 用户完成成本价上传操作
6. 系统自动将临时记忆提交到永久存储

### 后续使用
1. 用户输入款号（简单或完整）和颜色规格
2. 系统智能匹配记忆：
   - 简单款号优先匹配完整款号记忆
   - 避免多供应商相同款号的冲突
   - 选择置信度最高的匹配结果
3. 如果找到匹配记忆，自动选择之前的颜色并确认
4. 用户无需手动操作，直接进入下一步

## 配置选项

### 智能记忆设置
- **启用智能记忆功能**：控制整个功能的开关
- **上传时自动保存记忆**：控制是否在上传时自动保存临时记忆
- **自动确认置信度阈值**：设置自动确认的最低置信度要求（0.0-1.0）

### 记忆管理
- **记忆列表**：表格形式显示所有记忆记录
  - 款号：商品款号
  - 颜色规格：颜色规格描述
  - 确认颜色：用户确认的颜色列表（显示为"款号-颜色"格式，便于区分）
  - 使用次数：该记忆被使用的次数
  - 最后使用：最后一次使用的时间
  - 置信度：记忆的可信度（使用次数越多，置信度越高）

### 操作功能
- **删除选中**：删除选中的记忆记录
- **导出记忆**：将所有记忆导出为JSON文件
- **导入记忆**：从JSON文件导入记忆数据
- **清空所有**：清空所有记忆数据

## 数据结构

### 记忆记录格式
```json
{
  "sku_code": "C5B42591",                                    // 用户输入的款号
  "color_spec": "2024-夏季-套装",                            // 颜色规格
  "confirmed_colors": [                                      // 实际选中的完整SKU列表
    "GT158-C5B42591-卡其两件套",
    "GT158-C5B42591-两件套装"
  ],
  "supplier": "GT158",                                       // 供应商
  "file_pattern": "image.jpg",                               // 文件名模式
  "created_time": "2025-07-13T...",                          // 创建时间
  "last_used": "2025-07-13T...",                             // 最后使用时间
  "usage_count": 3,                                          // 使用次数
  "confidence": 0.95,                                        // 置信度
  "notes": "行1确认"                                          // 备注
}
```

### 双重记忆示例
当用户输入简单款号"C5B42591"，实际选择"GT158-C5B42591-卡其两件套"时：

**简单款号记忆**：
```
记忆键: "C5B42591|2024-夏季-套装"
确认颜色: ["GT158-C5B42591-卡其两件套", "GT158-C5B42591-两件套装"]
```

**完整款号记忆**：
```
记忆键: "GT158-C5B42591|2024-夏季-套装"
确认颜色: ["GT158-C5B42591-卡其两件套", "GT158-C5B42591-两件套装"]
```

### 存储文件
- **位置**：`data/smart_memory.json`
- **备份**：启用备份时自动创建`.backup`文件
- **格式**：JSON格式，包含版本信息、设置和记忆数据

## 技术实现

### 核心组件
1. **SmartMemoryManager**：智能记忆管理器，负责数据存储和检索
2. **ColorConfirmPanel**：颜色确认面板，集成智能记忆功能
3. **ConfigUIManager**：配置界面管理器，提供记忆管理界面

### 关键方法
- `add_temp_memory()`：添加临时记忆
- `commit_temp_memories()`：提交临时记忆到永久存储
- `find_matching_memory()`：查找匹配的记忆
- `check_smart_memory_auto_confirm()`：检查并执行自动确认

### 集成点
- **颜色确认**：在`confirm_selection()`方法中添加记忆
- **自动确认**：在`load_confirmation_data()`方法中检查记忆
- **记忆提交**：在成本价更新完成时自动提交

## 注意事项

1. **数据安全**：记忆数据存储在本地，建议定期备份
2. **性能影响**：记忆数量过多可能影响查找性能，建议定期清理
3. **匹配精度**：目前使用精确匹配，未来可考虑模糊匹配
4. **版本兼容**：导入记忆时注意版本兼容性

## 故障排除

### 常见问题
1. **记忆不生效**：检查智能记忆功能是否启用
2. **自动确认失败**：检查置信度阈值设置
3. **记忆丢失**：检查数据文件是否存在和完整
4. **导入失败**：检查JSON文件格式是否正确

### 解决方案
1. 在配置设置中检查智能记忆选项卡
2. 查看记忆列表确认数据是否正确
3. 使用导出功能备份重要记忆
4. 必要时清空记忆重新开始

---

**智能记忆功能让您的工作更高效！** 🚀
