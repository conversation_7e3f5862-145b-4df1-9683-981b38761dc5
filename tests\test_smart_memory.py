#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能记忆功能测试模块
"""

import unittest
import tempfile
import os
import json
from unittest.mock import Mock, patch
from modules.smart_memory_manager import SmartMemoryManager, MemoryRule


class TestSmartMemoryManager(unittest.TestCase):
    """智能记忆管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        self.temp_file.close()
        
        # 创建模拟的配置管理器和日志器
        self.mock_config_manager = Mock()
        self.mock_logger = Mock()
        
        # 创建智能记忆管理器实例
        self.memory_manager = SmartMemoryManager(
            config_manager=self.mock_config_manager,
            logger=self.mock_logger,
            memory_file=self.temp_file.name
        )
    
    def tearDown(self):
        """测试后清理"""
        # 删除临时文件
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_init_creates_default_file(self):
        """测试初始化时创建默认文件"""
        self.assertTrue(os.path.exists(self.temp_file.name))
        
        with open(self.temp_file.name, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.assertIn('version', data)
        self.assertIn('settings', data)
        self.assertIn('memories', data)
    
    def test_save_memory_rule(self):
        """测试保存记忆规则"""
        # 测试数据
        sku_code = "7922"
        color_spec = "黑色 M"
        price = 89.00
        selected_colors = ["黑色套装", "黑色两件套"]
        
        # 保存记忆规则
        result = self.memory_manager.save_memory_rule(
            sku_code=sku_code,
            color_spec=color_spec,
            price=price,
            selected_colors=selected_colors,
            notes="测试记忆规则"
        )
        
        self.assertTrue(result)
        
        # 验证记忆规则是否保存
        rule_key = f"{sku_code}|{color_spec}|{price:.2f}"
        self.assertIn(rule_key, self.memory_manager.memory_cache)
        
        saved_rule = self.memory_manager.memory_cache[rule_key]
        self.assertEqual(saved_rule.sku_code, sku_code)
        self.assertEqual(saved_rule.color_spec, color_spec)
        self.assertEqual(saved_rule.price, price)
        self.assertEqual(saved_rule.selected_colors, selected_colors)
    
    def test_check_memory_rules_exact_match(self):
        """测试精确匹配记忆规则"""
        # 先保存一个记忆规则
        sku_code = "7922"
        color_spec = "黑色 M"
        price = 89.00
        selected_colors = ["黑色套装"]
        
        self.memory_manager.save_memory_rule(
            sku_code=sku_code,
            color_spec=color_spec,
            price=price,
            selected_colors=selected_colors
        )
        
        # 测试精确匹配
        result = self.memory_manager.check_memory_rules(sku_code, color_spec, price)
        self.assertIsNotNone(result)
        self.assertEqual(result.sku_code, sku_code)
        self.assertEqual(result.selected_colors, selected_colors)
    
    def test_check_memory_rules_price_tolerance(self):
        """测试价格容差匹配"""
        # 先保存一个记忆规则
        sku_code = "7922"
        color_spec = "黑色 M"
        price = 100.00
        selected_colors = ["黑色套装"]
        
        self.memory_manager.save_memory_rule(
            sku_code=sku_code,
            color_spec=color_spec,
            price=price,
            selected_colors=selected_colors
        )
        
        # 测试价格容差匹配（±5%）
        test_price = 102.00  # 2%差异，应该匹配
        result = self.memory_manager.check_memory_rules(sku_code, color_spec, test_price)
        self.assertIsNotNone(result)
        
        # 测试超出容差范围
        test_price = 110.00  # 10%差异，不应该匹配
        result = self.memory_manager.check_memory_rules(sku_code, color_spec, test_price)
        self.assertIsNone(result)
    
    def test_delete_memory_rule(self):
        """测试删除记忆规则"""
        # 先保存一个记忆规则
        sku_code = "7922"
        color_spec = "黑色 M"
        price = 89.00
        selected_colors = ["黑色套装"]
        
        self.memory_manager.save_memory_rule(
            sku_code=sku_code,
            color_spec=color_spec,
            price=price,
            selected_colors=selected_colors
        )
        
        rule_key = f"{sku_code}|{color_spec}|{price:.2f}"
        
        # 验证规则存在
        self.assertIn(rule_key, self.memory_manager.memory_cache)
        
        # 删除规则
        result = self.memory_manager.delete_memory_rule(rule_key)
        self.assertTrue(result)
        
        # 验证规则已删除
        self.assertNotIn(rule_key, self.memory_manager.memory_cache)
    
    def test_update_memory_rule(self):
        """测试更新记忆规则"""
        # 先保存一个记忆规则
        sku_code = "7922"
        color_spec = "黑色 M"
        price = 89.00
        selected_colors = ["黑色套装"]
        
        self.memory_manager.save_memory_rule(
            sku_code=sku_code,
            color_spec=color_spec,
            price=price,
            selected_colors=selected_colors
        )
        
        rule_key = f"{sku_code}|{color_spec}|{price:.2f}"
        
        # 更新规则
        update_data = {
            "selected_colors": ["黑色套装", "黑色两件套"],
            "notes": "更新后的备注"
        }
        
        result = self.memory_manager.update_memory_rule(rule_key, update_data)
        self.assertTrue(result)
        
        # 验证更新结果
        updated_rule = self.memory_manager.memory_cache[rule_key]
        self.assertEqual(updated_rule.selected_colors, ["黑色套装", "黑色两件套"])
        self.assertEqual(updated_rule.notes, "更新后的备注")
    
    def test_get_all_memory_rules(self):
        """测试获取所有记忆规则"""
        # 保存多个记忆规则
        test_rules = [
            ("7922", "黑色 M", 89.00, ["黑色套装"]),
            ("7923", "白色 L", 99.00, ["白色套装"]),
            ("7924", "红色 S", 79.00, ["红色套装"])
        ]
        
        for sku_code, color_spec, price, selected_colors in test_rules:
            self.memory_manager.save_memory_rule(
                sku_code=sku_code,
                color_spec=color_spec,
                price=price,
                selected_colors=selected_colors
            )
        
        # 获取所有规则
        all_rules = self.memory_manager.get_all_memory_rules()
        self.assertEqual(len(all_rules), 3)
        
        # 验证规则内容
        for sku_code, color_spec, price, selected_colors in test_rules:
            rule_key = f"{sku_code}|{color_spec}|{price:.2f}"
            self.assertIn(rule_key, all_rules)
            self.assertEqual(all_rules[rule_key].sku_code, sku_code)
    
    def test_settings_management(self):
        """测试设置管理"""
        # 获取默认设置
        settings = self.memory_manager.get_settings()
        self.assertIn('enabled', settings)
        self.assertIn('auto_save_on_upload', settings)
        
        # 更新设置
        new_settings = {
            'enabled': False,
            'max_memories': 500
        }
        
        result = self.memory_manager.update_settings(new_settings)
        self.assertTrue(result)
        
        # 验证设置更新
        updated_settings = self.memory_manager.get_settings()
        self.assertFalse(updated_settings['enabled'])
        self.assertEqual(updated_settings['max_memories'], 500)
    
    def test_disabled_memory_functionality(self):
        """测试禁用记忆功能"""
        # 禁用记忆功能
        self.memory_manager.update_settings({'enabled': False})
        
        # 尝试保存记忆规则
        result = self.memory_manager.save_memory_rule(
            sku_code="7922",
            color_spec="黑色 M",
            price=89.00,
            selected_colors=["黑色套装"]
        )
        
        self.assertFalse(result)
        
        # 尝试检查记忆规则
        result = self.memory_manager.check_memory_rules("7922", "黑色 M", 89.00)
        self.assertIsNone(result)


class TestMemoryRule(unittest.TestCase):
    """记忆规则数据结构测试类"""
    
    def test_memory_rule_creation(self):
        """测试记忆规则创建"""
        rule = MemoryRule(
            sku_code="7922",
            color_spec="黑色 M",
            price=89.00,
            selected_colors=["黑色套装", "黑色两件套"],
            supplier="测试供应商",
            notes="测试备注"
        )
        
        self.assertEqual(rule.sku_code, "7922")
        self.assertEqual(rule.color_spec, "黑色 M")
        self.assertEqual(rule.price, 89.00)
        self.assertEqual(rule.selected_colors, ["黑色套装", "黑色两件套"])
        self.assertEqual(rule.supplier, "测试供应商")
        self.assertEqual(rule.notes, "测试备注")
        self.assertEqual(rule.usage_count, 0)
        self.assertEqual(rule.confidence, 1.0)


if __name__ == '__main__':
    unittest.main()
