"""
图像管理器 - 负责图像预览、缩略图、全屏显示等
重构自原始pyqt5_main_gui.py的图像处理部分
"""

import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from modules.ui_constants import UIConstants
from modules.image_splitter import ImageInfo
from modules.gui.image_card_widget import ImageCardWidget
from modules.gui.image_split_overlay import ImageSplitOverlay


class ImageManager:
    """图像管理器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.current_zoom = 1.0
        self.current_image_path = None
        
    def create_left_image_panel(self, parent):
        """创建左侧图像预览区域"""
        left_widget = QWidget()
        parent.addWidget(left_widget)

        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(10)

        # 图像预览区域
        self.create_main_image_preview_section_for_column1(left_layout)

        # AI处理按钮
        self.main_window.ai_processor_ui.create_ai_process_buttons(left_layout)

    def create_main_image_preview_section_for_column1(self, layout):
        """创建主图像预览区域（为列1设计）"""
        # 图像预览容器 - 简洁无边框设计
        image_container = QWidget()
        image_container.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
            }
        """)
        layout.addWidget(image_container)

        image_layout = QVBoxLayout(image_container)
        image_layout.setContentsMargins(0, 0, 0, 0)
        image_layout.setSpacing(8)

        # 移除顶部操作栏，文件选择和粘贴按钮已移动到AI处理按钮区域

        # 创建图像显示区域
        self.create_image_display_area(image_layout)

        # 移除底部操作栏，所有功能都集成到卡片上

    def create_image_display_area(self, layout):
        """创建图像显示区域"""
        # 创建堆叠窗口来切换缩略图和全屏视图
        self.image_stack = QStackedWidget()
        layout.addWidget(self.image_stack)

        # 创建缩略图区域
        self.create_main_thumbnail_area()

        # 创建全屏预览区域
        self.create_main_fullscreen_area()

        # 设置初始视图为缩略图
        self.image_stack.setCurrentIndex(0)

    def create_main_thumbnail_area(self):
        """创建主缩略图区域"""
        self.main_thumbnail_widget = QWidget()
        self.image_stack.addWidget(self.main_thumbnail_widget)

        thumbnail_layout = QVBoxLayout(self.main_thumbnail_widget)

        # 顶部控制栏 - 隐藏文件计数标题
        control_bar = QHBoxLayout()

        # 图像信息标签 - 隐藏不显示
        self.image_count_label = QLabel("文件: 0")
        self.image_count_label.setStyleSheet("color: #ffffff; font-weight: bold;")
        self.image_count_label.setVisible(False)  # 隐藏标题
        # control_bar.addWidget(self.image_count_label)  # 不添加到布局中

        control_bar.addStretch()

        # 只有在有内容时才添加控制栏
        # thumbnail_layout.addLayout(control_bar)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
        """)
        thumbnail_layout.addWidget(scroll_area)

        # 缩略图容器
        self.thumbnail_container = QWidget()
        scroll_area.setWidget(self.thumbnail_container)

        self.thumbnail_layout = QGridLayout(self.thumbnail_container)
        self.thumbnail_layout.setSpacing(10)

    def create_main_fullscreen_area(self):
        """创建主全屏预览区域"""
        self.main_fullscreen_widget = QWidget()
        self.image_stack.addWidget(self.main_fullscreen_widget)

        fullscreen_layout = QVBoxLayout(self.main_fullscreen_widget)

        # 顶部控制栏
        control_bar = QHBoxLayout()



        # 自定义分割按钮
        self.custom_split_btn = QPushButton("🔪 自定义分割")
        self.custom_split_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.custom_split_btn.clicked.connect(self.toggle_split_mode)
        control_bar.addWidget(self.custom_split_btn)

        # 图像信息显示
        self.image_info_label = QLabel("图像信息")
        self.image_info_label.setStyleSheet("color: #ffffff; font-weight: bold;")
        control_bar.addWidget(self.image_info_label)

        control_bar.addStretch()

        # 缩放控制
        self.zoom_in_btn = QPushButton("🔍+ 放大")
        self.zoom_in_btn.clicked.connect(self.zoom_in_main_image)
        control_bar.addWidget(self.zoom_in_btn)

        self.zoom_out_btn = QPushButton("🔍- 缩小")
        self.zoom_out_btn.clicked.connect(self.zoom_out_main_image)
        control_bar.addWidget(self.zoom_out_btn)

        # 🔥 修改：返回缩略图 -> 返回（文字精简）
        self.close_fullscreen_btn = QPushButton("◀ 返回")
        self.close_fullscreen_btn.clicked.connect(self.switch_to_thumbnail_view)
        control_bar.addWidget(self.close_fullscreen_btn)

        fullscreen_layout.addLayout(control_bar)

        # 🔥 新增：创建图像容器用于放置透明悬浮按钮
        self.image_container_widget = QWidget()
        self.image_container_widget.setMinimumHeight(400)
        fullscreen_layout.addWidget(self.image_container_widget)

        # 使用绝对定位的布局管理器
        self.image_container_layout = QVBoxLayout(self.image_container_widget)
        self.image_container_layout.setContentsMargins(0, 0, 0, 0)

        # 图像显示区域 - 调整宽度适应700px左侧面板
        self.image_scroll_area = QScrollArea()
        self.image_scroll_area.setAlignment(Qt.AlignCenter)

        # 🔧 修改：调整固定宽度为680像素，适应700px左侧面板
        self.image_scroll_area.setFixedWidth(680)  # 适应700px容器，留20px边距
        self.image_scroll_area.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)  # 宽度固定，高度自适应
        
        self.image_scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 5px;
            }
        """)
        self.image_container_layout.addWidget(self.image_scroll_area)

        # 图像标签
        self.main_image_label = QLabel()
        self.main_image_label.setAlignment(Qt.AlignCenter)
        self.main_image_label.setStyleSheet("background-color: #2b2b2b;")
        self.image_scroll_area.setWidget(self.main_image_label)

        # 🔥 新增：创建分割线overlay组件（父容器为图像标签）
        self.split_overlay = ImageSplitOverlay(self.main_image_label)
        self.split_overlay.split_completed.connect(self.on_split_completed)
        self.split_overlay.split_mode_changed.connect(self.on_split_mode_changed)
        self.split_overlay.hide()  # 初始隐藏

        # 🔥 新增：创建分割操作按钮
        self.create_split_operation_buttons()

        # 🔥 新增：创建透明悬浮的导航按钮
        self.create_navigation_buttons()

    def create_navigation_buttons(self):
        """创建透明悬浮的导航按钮"""
        # 上一张按钮
        self.prev_image_btn = QPushButton("◀")
        self.prev_image_btn.setParent(self.image_container_widget)
        self.prev_image_btn.clicked.connect(self.navigate_to_previous_image)
        self.prev_image_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 0, 0, 120);
                color: white;
                border: 2px solid rgba(255, 255, 255, 100);
                border-radius: 25px;
                font-size: 18px;
                font-weight: bold;
                width: 50px;
                height: 50px;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 180);
                border: 2px solid rgba(255, 255, 255, 150);
                transform: scale(1.1);
            }
            QPushButton:pressed {
                background-color: rgba(0, 0, 0, 200);
                border: 2px solid rgba(255, 255, 255, 200);
            }
        """)
        self.prev_image_btn.setFixedSize(50, 50)

        # 下一张按钮
        self.next_image_btn = QPushButton("▶")
        self.next_image_btn.setParent(self.image_container_widget)
        self.next_image_btn.clicked.connect(self.navigate_to_next_image)
        self.next_image_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 0, 0, 120);
                color: white;
                border: 2px solid rgba(255, 255, 255, 100);
                border-radius: 25px;
                font-size: 18px;
                font-weight: bold;
                width: 50px;
                height: 50px;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 180);
                border: 2px solid rgba(255, 255, 255, 150);
                transform: scale(1.1);
            }
            QPushButton:pressed {
                background-color: rgba(0, 0, 0, 200);
                border: 2px solid rgba(255, 255, 255, 200);
            }
        """)
        self.next_image_btn.setFixedSize(50, 50)

        # 初始隐藏按钮，在有图像时才显示
        self.prev_image_btn.hide()
        self.next_image_btn.hide()

        # 连接resize事件以更新按钮位置
        self.image_container_widget.resizeEvent = self.on_image_container_resized

    def create_split_operation_buttons(self):
        """创建分割操作按钮（固定在图像容器底部）"""
        # 创建按钮容器
        self.split_buttons_widget = QWidget(self.image_container_widget)
        self.split_buttons_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(43, 43, 43, 200);
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 30);
            }
        """)

        # 按钮布局
        buttons_layout = QHBoxLayout(self.split_buttons_widget)
        buttons_layout.setContentsMargins(12, 8, 12, 8)
        buttons_layout.setSpacing(10)

        # 添加分割线按钮
        self.add_split_line_btn = QPushButton("➕ 添加")
        self.add_split_line_btn.setStyleSheet(self.get_split_button_style("#4CAF50"))
        self.add_split_line_btn.clicked.connect(self.add_split_line)
        buttons_layout.addWidget(self.add_split_line_btn)

        # 删除分割线按钮
        self.remove_split_line_btn = QPushButton("➖ 删除")
        self.remove_split_line_btn.setStyleSheet(self.get_split_button_style("#ff9800"))
        self.remove_split_line_btn.clicked.connect(self.remove_split_line)
        buttons_layout.addWidget(self.remove_split_line_btn)

        # 确认分割按钮
        self.confirm_split_btn = QPushButton("✓ 确认分割")
        self.confirm_split_btn.setStyleSheet(self.get_split_button_style("#2196F3"))
        self.confirm_split_btn.clicked.connect(self.confirm_split)
        buttons_layout.addWidget(self.confirm_split_btn)

        # 初始隐藏
        self.split_buttons_widget.hide()

    def get_split_button_style(self, color: str) -> str:
        """获取分割按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 12px;
                min-width: 70px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
            }}
        """

    def update_split_buttons_position(self):
        """更新分割操作按钮位置"""
        if hasattr(self, 'split_buttons_widget') and self.split_buttons_widget.isVisible():
            # 计算位置（底部居中）
            container_width = self.image_container_widget.width()
            container_height = self.image_container_widget.height()

            widget_width = self.split_buttons_widget.sizeHint().width()
            widget_height = self.split_buttons_widget.sizeHint().height()

            x = (container_width - widget_width) // 2
            y = container_height - widget_height - 15

            self.split_buttons_widget.move(x, y)
            self.split_buttons_widget.resize(widget_width, widget_height)

    def on_image_container_resized(self, event):
        """图像容器大小变化时更新按钮位置"""
        try:
            if hasattr(self, 'prev_image_btn') and hasattr(self, 'next_image_btn'):
                container_width = self.image_container_widget.width()
                container_height = self.image_container_widget.height()
                
                # 计算按钮位置（垂直居中，水平在边缘）
                button_y = (container_height - 50) // 2
                
                # 上一张按钮：左边距离边缘20像素
                self.prev_image_btn.move(20, button_y)
                
                # 下一张按钮：右边距离边缘20像素
                self.next_image_btn.move(container_width - 70, button_y)

            # 更新分割操作按钮位置
            self.update_split_buttons_position()

        except Exception as e:
            self.main_window.log_message(f"更新导航按钮位置失败: {str(e)}", "ERROR")

    def get_current_image_index(self):
        """获取当前图像在文件列表中的索引"""
        try:
            if not self.current_image_path:
                return -1
                
            files = self.main_window.file_manager.get_file_list()
            if self.current_image_path in files:
                return files.index(self.current_image_path)
            return -1
            
        except Exception as e:
            self.main_window.log_message(f"获取当前图像索引失败: {str(e)}", "ERROR")
            return -1

    def navigate_to_previous_image(self):
        """切换到上一张图像"""
        try:
            files = self.main_window.file_manager.get_file_list()
            if len(files) <= 1:
                return
                
            current_index = self.get_current_image_index()
            if current_index <= 0:
                # 如果是第一张或未找到，切换到最后一张
                new_index = len(files) - 1
            else:
                new_index = current_index - 1
                
            new_file_path = files[new_index]
            self.switch_to_image(new_file_path)
            
        except Exception as e:
            self.main_window.log_message(f"切换到上一张图像失败: {str(e)}", "ERROR")

    def navigate_to_next_image(self):
        """切换到下一张图像"""
        try:
            files = self.main_window.file_manager.get_file_list()
            if len(files) <= 1:
                return
                
            current_index = self.get_current_image_index()
            if current_index >= len(files) - 1 or current_index == -1:
                # 如果是最后一张或未找到，切换到第一张
                new_index = 0
            else:
                new_index = current_index + 1
                
            new_file_path = files[new_index]
            self.switch_to_image(new_file_path)
            
        except Exception as e:
            self.main_window.log_message(f"切换到下一张图像失败: {str(e)}", "ERROR")

    def switch_to_image(self, file_path):
        """切换到指定的图像并同步相关UI"""
        try:
            # 更新当前图像路径
            self.current_image_path = file_path
            
            # 🔥 绑定现有逻辑：同步解析列表和表格选择
            self.main_window.sync_result_table_selection(file_path)
            
            # 更新缩略图选择状态
            self.update_card_selections(file_path)
            
            # 重新加载全屏图像
            self.load_main_fullscreen_image()
            
            # 更新导航按钮可见性
            self.update_navigation_buttons_visibility()
            
            # 记录日志
            display_name = self.main_window.get_display_name_for_file(file_path)
            self.main_window.log_message(f"切换到图像: {display_name}")
            
        except Exception as e:
            self.main_window.log_message(f"切换图像失败: {str(e)}", "ERROR")

    def update_navigation_buttons_visibility(self):
        """更新导航按钮的可见性"""
        try:
            files = self.main_window.file_manager.get_file_list()
            has_multiple_images = len(files) > 1
            
            # 只有多张图像时才显示导航按钮
            if hasattr(self, 'prev_image_btn') and hasattr(self, 'next_image_btn'):
                if has_multiple_images and self.image_stack.currentIndex() == 1:  # 全屏模式
                    self.prev_image_btn.show()
                    self.next_image_btn.show()
                    # 更新按钮位置
                    self.on_image_container_resized(None)
                else:
                    self.prev_image_btn.hide()
                    self.next_image_btn.hide()
            
        except Exception as e:
            self.main_window.log_message(f"更新导航按钮可见性失败: {str(e)}", "ERROR")

    def switch_to_thumbnail_view(self):
        """切换到缩略图视图"""
        self.image_stack.setCurrentIndex(0)
        # 🔥 新增：隐藏导航按钮
        if hasattr(self, 'prev_image_btn') and hasattr(self, 'next_image_btn'):
            self.prev_image_btn.hide()
            self.next_image_btn.hide()

    def switch_to_fullscreen_view(self):
        """切换到全屏视图"""
        self.image_stack.setCurrentIndex(1)
        self.load_main_fullscreen_image()
        # 🔥 新增：显示导航按钮（如果有多张图像）
        self.update_navigation_buttons_visibility()

    def load_main_fullscreen_image(self):
        """加载主全屏图像 - 使用自适应缩放策略"""
        if not self.current_image_path or not os.path.exists(self.current_image_path):
            self.main_image_label.setText("未选择图像或文件不存在")
            return

        try:
            pixmap = QPixmap(self.current_image_path)
            if not pixmap.isNull():
                # 🔧 修改：使用自适应缩放
                self.current_zoom = self.calculate_auto_fit_zoom(pixmap)
                self.update_main_fullscreen_display()
                
                # 🔥 新增：更新导航按钮可见性
                self.update_navigation_buttons_visibility()
                
                # 降级为DEBUG级别，用户在界面上已能看到图像加载
                self.main_window.log_message(f"加载全屏图像: {self.main_window.get_display_name_for_file(self.current_image_path)}", "DEBUG")
            else:
                self.main_image_label.setText("无法加载图像")
        except Exception as e:
            self.main_window.log_message(f"加载全屏图像失败: {str(e)}", "ERROR")
            self.main_image_label.setText("加载图像失败")

    def calculate_auto_fit_zoom(self, pixmap):
        """计算自适应缩放比例，使图像适应容器尺寸并保持宽高比"""
        try:
            # 🔧 修改：调整最大宽度为660像素，适应680px容器
            MAX_WIDTH = 660  # 680容器宽度减去滚动条等空间
            
            # 🔧 新增：动态获取滚动区域的实际可用高度
            if hasattr(self, 'image_scroll_area') and self.image_scroll_area:
                # 获取滚动区域的当前高度，减去一些边距
                container_height = self.image_scroll_area.height() - 20  # 减去边距
                MAX_HEIGHT = max(400, container_height)  # 最小400像素，避免过小
            else:
                MAX_HEIGHT = 600  # 默认高度
            
            original_width = pixmap.width()
            original_height = pixmap.height()
            
            if original_width == 0 or original_height == 0:
                return 1.0
            
            # 计算宽度和高度的缩放比例
            width_scale = MAX_WIDTH / original_width
            height_scale = MAX_HEIGHT / original_height
            
            # 选择较小的缩放比例以确保图像完全适应容器，保持宽高比
            optimal_scale = min(width_scale, height_scale)
            
            # 降级为DEBUG级别，这是内部缩放计算过程
            self.main_window.log_message(
                f"🎯 智能缩放计算: 容器({MAX_WIDTH}x{MAX_HEIGHT}) "
                f"图像({original_width}x{original_height}) "
                f"缩放比例({optimal_scale:.3f})", "DEBUG"
            )
            
            return optimal_scale
            
        except Exception as e:
            self.main_window.log_message(f"计算自适应缩放失败: {str(e)}", "ERROR")
            return 1.0

    def update_main_fullscreen_display(self):
        """更新主全屏显示"""
        if not self.current_image_path:
            return

        try:
            pixmap = QPixmap(self.current_image_path)
            if not pixmap.isNull():
                # 🔧 修改：使用固定的缩放逻辑，保持图像不变形
                scaled_pixmap = pixmap.scaled(
                    pixmap.size() * self.current_zoom, 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                
                self.main_image_label.setPixmap(scaled_pixmap)
                self.main_image_label.resize(scaled_pixmap.size())

                # 🔥 新增：更新分割overlay的几何位置和尺寸
                if hasattr(self, 'split_overlay') and self.split_overlay.is_split_mode:
                    self.update_overlay_geometry()
                    # 更新overlay的显示尺寸信息
                    self.split_overlay.update_display_size((scaled_pixmap.width(), scaled_pixmap.height()))

                # 🔥 删除：不再更新信息标签显示
                # self.image_info_label.setText(
                #     f"图像: {QFileInfo(self.current_image_path).fileName()} | "
                #     f"尺寸: {pixmap.width()}x{pixmap.height()} | "
                #     f"缩放: {int(self.current_zoom * 100)}% | "
                #     f"显示: {scaled_pixmap.width()}x{scaled_pixmap.height()}"
                # )
        except Exception as e:
            self.main_window.log_message(f"更新全屏显示失败: {str(e)}", "ERROR")

    def zoom_in_main_image(self):
        """放大主图像"""
        self.current_zoom = min(self.current_zoom * 1.2, 5.0)
        self.update_main_fullscreen_display()

    def zoom_out_main_image(self):
        """缩小主图像"""
        self.current_zoom = max(self.current_zoom / 1.2, 0.1)
        self.update_main_fullscreen_display()

    def update_main_thumbnail_preview(self):
        """更新主缩略图预览 - 使用新的卡片组件"""
        # 清空现有缩略图
        self.clear_main_thumbnails()
        
        # 获取文件列表
        files = self.main_window.file_manager.get_file_list()
        
        # 更新文件计数
        self.image_count_label.setText(f"文件: {len(files)}")
        
        if not files:
            return
            
        # 创建缩略图网格 - 每行4个卡片
        cols = 4
        for i, file_path in enumerate(files):
            row = i // cols
            col = i % cols
            
            try:
                # 获取显示名称
                display_name = self.get_display_name_for_file(file_path)
                
                # 创建新的图像卡片
                card = ImageCardWidget(file_path, display_name, self.thumbnail_container)
                
                # 连接信号
                card.card_clicked.connect(self.on_card_clicked)
                card.card_double_clicked.connect(self.on_card_double_clicked)
                card.delete_requested.connect(self.on_delete_requested)
                card.split_requested.connect(self.on_split_requested)
                card.parse_requested.connect(self.on_parse_requested)
                card.rename_requested.connect(self.on_rename_requested)
                
                self.thumbnail_layout.addWidget(card, row, col)
                
            except Exception as e:
                self.main_window.log_message(f"创建图像卡片失败 {file_path}: {str(e)}", "ERROR")

    # 新的卡片事件处理方法
    def on_card_clicked(self, file_path):
        """缩略图卡片点击事件"""
        try:
            # 🔥 新增：切换图像前先保存当前ERP状态
            if hasattr(self.main_window, 'table_ui_manager'):
                self.main_window.table_ui_manager.save_current_erp_state()

            # 设置为当前选中的图像
            self.current_image_path = file_path
            self.main_window.current_selected_file = file_path

            # 更新卡片选择状态
            self.update_card_selections(file_path)

            # 如果在全屏模式，更新全屏图像显示
            if self.image_stack.currentIndex() == 1:  # 全屏模式
                self.load_main_fullscreen_image()

            # 🔥 新增：切换图像后恢复目标图像的ERP状态
            if hasattr(self.main_window, 'table_ui_manager'):
                self.main_window.table_ui_manager.restore_erp_state_for_file(file_path)

            # 同步缩略图选择（这会触发表格和解析结果的同步）
            self.main_window.sync_thumbnail_selection(file_path)

            # 降级为DEBUG级别，用户在界面上已能看到选中状态
            self.main_window.log_message(f"选中图像: {self.main_window.get_display_name_for_file(file_path)}", "DEBUG")
        except Exception as e:
            self.main_window.log_message(f"处理图像点击事件失败: {str(e)}", "ERROR")
    
    def on_card_double_clicked(self, file_path):
        """卡片双击事件 - 切换到全屏预览"""
        self.current_image_path = file_path
        self.switch_to_fullscreen_view()
    
    def on_delete_requested(self, file_path):
        """删除请求事件（已在卡片中确认，此处直接删除）"""
        try:
            # 从文件管理器中移除（使用新的路径删除方法）
            success = self.main_window.file_manager.remove_file_by_path(file_path)

            if success:
                # 清理重命名缓存
                if hasattr(self.main_window, 'image_rename_cache'):
                    self.main_window.image_rename_cache.pop(file_path, None)

                # 🔥 重要修复：删除图像时清空相关表格数据
                self.clear_related_table_data(file_path)

                # 更新预览
                self.update_main_thumbnail_preview()

                # 如果是当前选中的图像，清空选择
                if self.current_image_path == file_path:
                    self.current_image_path = None

                self.main_window.log_message(f"已删除图像: {self.main_window.get_display_name_for_file(file_path)}")
            else:
                self.main_window.log_message(f"删除图像失败: 文件不在管理列表中", "ERROR")

        except Exception as e:
            self.main_window.log_message(f"删除图像失败: {str(e)}", "ERROR")

    def clear_related_table_data(self, file_path):
        """清空与删除文件相关的表格数据"""
        try:
            # 清空商品明细表格（拿货和退货）
            if hasattr(self.main_window, 'table_ui_manager'):
                self.main_window.table_ui_manager.clear_all_tables()
                self.main_window.log_message("已清空相关表格数据")

            # 清空解析结果表格中对应的行
            if hasattr(self.main_window, 'table_ui_manager') and hasattr(self.main_window.table_ui_manager, 'result_table'):
                result_table = self.main_window.table_ui_manager.result_table
                rows_to_remove = []

                # 查找包含该文件路径的行
                for row in range(result_table.rowCount()):
                    file_item = result_table.item(row, 1)  # 文件名列
                    if file_item:
                        display_name = file_item.text()
                        # 检查是否匹配（考虑重命名的情况）
                        if (os.path.basename(file_path) in display_name or
                            self.main_window.get_display_name_for_file(file_path) == display_name):
                            rows_to_remove.append(row)

                # 从后往前删除行（避免索引变化）
                for row in reversed(rows_to_remove):
                    result_table.removeRow(row)
                    self.main_window.log_message(f"已删除解析结果第{row+1}行")

                # 重新编号剩余行
                for i in range(result_table.rowCount()):
                    result_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))

            # 🔥 重要修复：清空当前ERP匹配结果（仅针对当前会话）
            if hasattr(self.main_window, 'erp_match_results'):
                self.main_window.erp_match_results.clear()

            # 🔥 新增：清空该文件对应的ERP状态缓存
            if hasattr(self.main_window, 'erp_states_cache') and file_path in self.main_window.erp_states_cache:
                del self.main_window.erp_states_cache[file_path]
                print(f"🗑️ 已清理文件 {os.path.basename(file_path)} 的ERP状态缓存")

            # 🔥 新增：清空该文件对应的存储结果
            if hasattr(self.main_window, 'stored_results') and file_path in self.main_window.stored_results:
                del self.main_window.stored_results[file_path]
                print(f"🗑️ 已清理文件 {os.path.basename(file_path)} 的存储结果")

        except Exception as e:
            self.main_window.log_message(f"清空相关表格数据失败: {str(e)}", "ERROR")
    
    def on_split_requested(self, file_path):
        """处理分割请求"""
        try:
            self.main_window.log_message(f"分割图像: {self.get_display_name_for_file(file_path)}")
            # 这里调用主窗口的分割方法
            if hasattr(self.main_window, 'split_specific_image'):
                self.main_window.split_specific_image(file_path)
            else:
                self.main_window.log_message("分割功能暂未实现", "WARNING")
        except Exception as e:
            self.main_window.log_message(f"分割请求处理失败: {str(e)}", "ERROR")

    def on_parse_requested(self, file_path):
        """处理解析请求"""
        try:
            self.main_window.log_message(f"解析图像: {self.get_display_name_for_file(file_path)}")
            # 调用主窗口的选定图像AI解析方法
            if hasattr(self.main_window, 'start_ai_processing_selected'):
                # 先设置当前图像路径
                self.current_image_path = file_path
                # 然后开始解析
                self.main_window.start_ai_processing_selected()
            else:
                self.main_window.log_message("解析功能暂未实现", "WARNING")
        except Exception as e:
            self.main_window.log_message(f"解析请求处理失败: {str(e)}", "ERROR")

    def on_rename_requested(self, file_path, new_name):
        """重命名请求事件"""
        try:
            # 保存重命名信息到重命名缓存
            if not hasattr(self.main_window, 'image_rename_cache'):
                self.main_window.image_rename_cache = {}
            
            original_name = os.path.splitext(os.path.basename(file_path))[0]
            
            self.main_window.image_rename_cache[file_path] = {
                'display_name': new_name,
                'supplier_code': self.extract_supplier_code(new_name),
                'is_renamed': True,
                'original_name': original_name
            }

            # 更新卡片显示
            self.update_main_thumbnail_preview()

            # 自动关联供应商
            supplier_code = self.extract_supplier_code(new_name)
            if supplier_code:
                self.main_window.log_message(f"图像已重命名为 '{new_name}' 并识别供应商代码 '{supplier_code}'")
            else:
                self.main_window.log_message(f"图像已重命名为 '{new_name}'")

        except Exception as e:
            self.main_window.log_message(f"重命名失败: {str(e)}", "ERROR")
    
    def update_card_selections(self, selected_file_path):
        """更新所有卡片的选中状态"""
        for i in range(self.thumbnail_layout.count()):
            item = self.thumbnail_layout.itemAt(i)
            if item and item.widget():
                card = item.widget()
                if isinstance(card, ImageCardWidget):
                    is_selected = card.file_path == selected_file_path
                    card.set_selected(is_selected)
                    # 强制重绘确保样式更新
                    card.update()

    def clear_main_thumbnails(self):
        """清空主缩略图"""
        while self.thumbnail_layout.count():
            item = self.thumbnail_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

    def analyze_uploaded_image(self, file_path: str):
        """分析上传的图像"""
        try:
            # 使用image_splitter来分析图像
            image_info = self.main_window.image_splitter.analyze_image(file_path)
            
            # 更新当前图像信息
            self.main_window.current_image_info = image_info
            
            # 更新图像信息显示
            self.update_image_info_display(image_info)
            
            # 记录日志
            file_name = QFileInfo(file_path).fileName()
            # 降级为DEBUG级别，这是内部分析过程
            self.main_window.log_message(f"分析图像: {file_name}", "DEBUG")
            
        except Exception as e:
            self.main_window.log_message(f"分析图像失败: {str(e)}", "ERROR")

    def update_image_info_display(self, image_info: ImageInfo):
        """更新图像信息显示"""
        try:
            # 更新预览
            self.update_main_thumbnail_preview()
            
            # 设置当前图像路径
            self.current_image_path = image_info.file_path
            
        except Exception as e:
            self.main_window.log_message(f"更新图像信息显示失败: {str(e)}", "ERROR")

    def extract_supplier_code(self, name):
        """从名称中提取供应商代码"""
        if not name:
            return None

        # 已知的供应商代码模式
        supplier_codes = [
            "GT408", "GT251", "GT155", "GT253", "GT158", "GTAF05",
            "DSD258", "DSD129", "DSD106",
            "GD340"
        ]

        name_upper = name.upper()

        # 1. 精确匹配：检查文件名是否以供应商代码开头
        for code in supplier_codes:
            if name_upper.startswith(code):
                return code

        # 2. 包含匹配：检查文件名是否包含供应商代码
        for code in supplier_codes:
            if code in name_upper:
                return code

        # 3. 动态识别：字母+数字组合
        import re
        pattern = r'^([A-Z]{2,6}\d{2,4})'
        match = re.match(pattern, name_upper)
        if match:
            potential_code = match.group(1)
            if 3 <= len(potential_code) <= 10:
                return potential_code

        return None

    def get_display_name_for_file(self, file_path):
        """获取文件的显示名称（重命名后的名称或原始名称）"""
        try:
            # 检查重命名缓存
            if (hasattr(self.main_window, 'image_rename_cache') and
                file_path in self.main_window.image_rename_cache):
                rename_info = self.main_window.image_rename_cache[file_path]
                return rename_info.get('display_name', os.path.basename(file_path))
            else:
                return os.path.basename(file_path)
        except Exception:
            return os.path.basename(file_path)

    def toggle_split_mode(self):
        """切换分割模式"""
        try:
            if not self.current_image_path or not os.path.exists(self.current_image_path):
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self.main_window, "警告", "请先选择一个图像文件")
                return

            if hasattr(self, 'split_overlay') and self.split_overlay.is_split_mode:
                # 退出分割模式
                self.split_overlay.exit_split_mode()
            else:
                # 进入分割模式
                self.enter_split_mode()

        except Exception as e:
            self.main_window.log_message(f"切换分割模式失败: {str(e)}", "ERROR")

    def enter_split_mode(self):
        """进入分割模式"""
        try:
            if not self.current_image_path:
                return

            # 获取图像尺寸信息
            from PIL import Image
            with Image.open(self.current_image_path) as img:
                original_size = img.size  # (width, height)

            # 获取当前显示的图像尺寸
            if self.main_image_label.pixmap():
                pixmap = self.main_image_label.pixmap()
                display_size = (pixmap.width(), pixmap.height())
            else:
                display_size = original_size

            # 调整overlay大小以匹配图像显示区域
            self.update_overlay_geometry()

            # 进入分割模式
            self.split_overlay.enter_split_mode(
                self.current_image_path,
                (original_size[0], original_size[1]),  # 原图尺寸 (width, height)
                display_size
            )

            # 更新按钮文本
            self.custom_split_btn.setText("✕ 退出分割")
            self.custom_split_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ff4444;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #ff6666;
                }
                QPushButton:pressed {
                    background-color: #cc3333;
                }
            """)

            # 显示分割操作按钮
            if hasattr(self, 'split_buttons_widget'):
                self.split_buttons_widget.show()
                self.update_split_buttons_position()

            self.main_window.log_message(f"进入分割模式: {os.path.basename(self.current_image_path)}")

        except Exception as e:
            self.main_window.log_message(f"进入分割模式失败: {str(e)}", "ERROR")

    def update_overlay_geometry(self):
        """更新overlay几何位置以匹配图像显示区域"""
        if hasattr(self, 'split_overlay') and self.main_image_label.pixmap():
            # 由于overlay的父容器是图像标签，所以直接设置为标签的大小
            pixmap = self.main_image_label.pixmap()
            self.split_overlay.setGeometry(0, 0, pixmap.width(), pixmap.height())
            self.split_overlay.show()

    def on_split_mode_changed(self, is_split_mode: bool):
        """分割模式改变事件处理"""
        if not is_split_mode:
            # 退出分割模式，恢复按钮状态
            self.custom_split_btn.setText("🔪 自定义分割")
            self.custom_split_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
                QPushButton:pressed {
                    background-color: #3d8b40;
                }
            """)
            self.split_overlay.hide()

            # 隐藏分割操作按钮
            if hasattr(self, 'split_buttons_widget'):
                self.split_buttons_widget.hide()

    def on_split_completed(self, split_files):
        """分割完成事件处理"""
        try:
            # 将分割后的文件添加到文件管理器
            valid_files = [f for f in split_files if os.path.exists(f)]
            if valid_files:
                self.main_window.file_manager.add_files(valid_files)

            # 刷新缩略图显示
            self.update_main_thumbnail_preview()

            # 记录日志
            self.main_window.log_message(f"自定义分割完成，生成 {len(split_files)} 个文件")

            # 切换回缩略图视图以查看结果
            self.switch_to_thumbnail_view()

        except Exception as e:
            self.main_window.log_message(f"处理分割结果失败: {str(e)}", "ERROR")

    def add_split_line(self):
        """添加分割线"""
        if hasattr(self, 'split_overlay') and self.split_overlay.is_split_mode:
            self.split_overlay.add_split_line()

    def remove_split_line(self):
        """删除分割线"""
        if hasattr(self, 'split_overlay') and self.split_overlay.is_split_mode:
            self.split_overlay.remove_last_split_line()

    def confirm_split(self):
        """确认分割"""
        if hasattr(self, 'split_overlay') and self.split_overlay.is_split_mode:
            self.split_overlay.confirm_split()

