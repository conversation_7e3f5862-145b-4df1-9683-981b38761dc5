"""
图像卡片组件 - 现代化设计
"""

import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *


class ImageCardWidget(QWidget):
    """现代化图像卡片组件"""
    
    # 定义信号
    card_clicked = pyqtSignal(str)  # 卡片点击
    card_double_clicked = pyqtSignal(str)  # 卡片双击
    delete_requested = pyqtSignal(str)  # 删除请求
    split_requested = pyqtSignal(str)  # 分割请求
    parse_requested = pyqtSignal(str)  # 解析请求
    rename_requested = pyqtSignal(str, str)  # 重命名请求 (文件路径, 新名称)
    
    def __init__(self, file_path, display_name, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.display_name = display_name
        self.is_selected = False
        self.is_renaming = False
        self.is_hovered = False  # 添加悬停状态
        
        self.setFixedSize(190, 200)  # 进一步放大卡片尺寸以容纳更大按钮
        self.setup_ui()
        self.setup_styles()
        
    def setup_ui(self):
        """设置UI"""
        # 主容器，使用绝对定位
        # 移除 self.setLayout(None) 避免Qt布局错误
        
        # 创建缩略图标签 - 调整尺寸适应新卡片
        self.thumbnail_label = QLabel(self)
        self.thumbnail_label.setGeometry(15, 10, 160, 160)  # 调整到160x160
        self.thumbnail_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: #1a1a1b;
                border: 2px solid #444444;
                border-radius: 12px;
                padding: 2px;
            }
        """)
        
        # 加载缩略图
        self.load_thumbnail()
        
        # 创建文件名标签
        self.name_label = QLabel(self.display_name, self)
        self.name_label.setGeometry(5, 175, 180, 20)  # 调整位置适应新卡片
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                background: transparent;
                border: none;
            }
        """)
        
        # 创建分割按钮（左下角）- 位置调整到缩略图容器内底部
        self.split_btn = QPushButton("分割", self)
        self.split_btn.setGeometry(15, 140, 53, 28)  # 缩略图容器底部，Y=140确保在容器内
        self.split_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 123, 255, 0.9);
                color: white;
                border: none;
                border-radius: 0px;
                font-size: 11px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-weight: bold;
                padding: 0px;
                margin: 0px;
            }
            QPushButton:hover {
                background-color: rgba(0, 86, 179, 1.0);
                border: 1px solid rgba(255, 255, 255, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(0, 64, 133, 1.0);
            }
        """)
        self.split_btn.setVisible(False)
        self.split_btn.clicked.connect(self.on_split_clicked)

        # 创建解析按钮（中下角）- 位置调整到缩略图容器内底部，无缝连接
        self.parse_btn = QPushButton("解析", self)
        self.parse_btn.setGeometry(68, 140, 54, 28)  # 缩略图容器底部中间，无缝连接
        self.parse_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(40, 167, 69, 0.9);
                color: white;
                border: none;
                border-radius: 0px;
                font-size: 11px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-weight: bold;
                padding: 0px;
                margin: 0px;
            }
            QPushButton:hover {
                background-color: rgba(33, 136, 56, 1.0);
                border: 1px solid rgba(255, 255, 255, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(30, 126, 52, 1.0);
            }
        """)
        self.parse_btn.setVisible(False)
        self.parse_btn.clicked.connect(self.on_parse_clicked)

        # 创建删除按钮（右下角）- 位置调整到缩略图容器内底部，无缝连接
        self.delete_btn = QPushButton("删除", self)
        self.delete_btn.setGeometry(122, 140, 53, 28)  # 缩略图容器底部右侧，总宽度160px，无缝连接
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(239, 68, 68, 0.9);
                color: white;
                border: none;
                border-radius: 0px;
                font-size: 11px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-weight: bold;
                padding: 0px;
                margin: 0px;
            }
            QPushButton:hover {
                background-color: rgba(220, 38, 38, 1.0);
                border: 1px solid rgba(255, 255, 255, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(185, 28, 28, 1.0);
            }
        """)
        self.delete_btn.setVisible(False)
        self.delete_btn.clicked.connect(self.on_delete_clicked)
        
        # 确保按钮在最上层
        self.delete_btn.raise_()
        self.parse_btn.raise_()
        self.split_btn.raise_()
        
        # 启用鼠标跟踪以检测悬停
        self.setMouseTracking(True)
        
    def setup_styles(self):
        """设置样式"""
        self.update_selection_style()
        
    def update_selection_style(self):
        """更新选中状态样式"""
        if self.is_selected:
            # 选中状态：蓝色边框和阴影效果
            self.setStyleSheet("""
                QWidget {
                    background-color: #161617;
                    border: 2px solid #3b82f6;
                    border-radius: 8px;
                    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
                }
            """)
            # 更新缩略图边框为蓝色圆角
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    background-color: #1a1a1b;
                    border: 2px solid #3b82f6;
                    border-radius: 12px;
                    padding: 2px;
                }
            """)
        else:
            # 未选中状态：灰色边框
            self.setStyleSheet("""
                QWidget {
                    background-color: #161617;
                    border: 1px solid #374151;
                    border-radius: 8px;
                    transition: all 0.2s ease;
                }
                QWidget:hover {
                    border: 1px solid #6b7280;
                    background-color: #1a1a1b;
                }
            """)
            # 恢复缩略图默认边框圆角
            self.thumbnail_label.setStyleSheet("""
                QLabel {
                    background-color: #1a1a1b;
                    border: 1px solid #444444;
                    border-radius: 12px;
                    padding: 2px;
                }
            """)
        
        # 强制样式更新
        self.style().unpolish(self)
        self.style().polish(self)
    
    def load_thumbnail(self):
        """加载缩略图"""
        try:
            if os.path.exists(self.file_path):
                # 使用QTimer延迟加载，避免阻塞UI线程
                QTimer.singleShot(0, self._do_load_thumbnail)
            else:
                self.thumbnail_label.setText("文件不存在")
        except Exception as e:
            self.thumbnail_label.setText("加载失败")
            self.thumbnail_label.setStyleSheet(self.thumbnail_label.styleSheet() + """
                QLabel { 
                    color: #ff6b6b; 
                    font-size: 12px; 
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
            """)
            print(f"缩略图实际加载失败: {e}")
    
    def _do_load_thumbnail(self):
        """实际加载缩略图的方法"""
        try:
            pixmap = QPixmap(self.file_path)
            if not pixmap.isNull():
                # 计算合适的缩放尺寸，保持比例不变形
                label_size = self.thumbnail_label.size()
                available_width = label_size.width() - 4  # 减去padding
                available_height = label_size.height() - 4  # 减去padding
                
                # 缩放图像保持比例，适应容器
                scaled_pixmap = pixmap.scaled(
                    available_width, available_height, 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                self.thumbnail_label.setPixmap(scaled_pixmap)
            else:
                self.thumbnail_label.setText("无法加载")
                self.thumbnail_label.setStyleSheet(self.thumbnail_label.styleSheet() + """
                    QLabel { 
                        color: #888888; 
                        font-size: 12px; 
                        font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    }
                """)
        except Exception as e:
            self.thumbnail_label.setText("加载失败")
            self.thumbnail_label.setStyleSheet(self.thumbnail_label.styleSheet() + """
                QLabel { 
                    color: #ff6b6b; 
                    font-size: 12px; 
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
            """)
            print(f"缩略图实际加载失败: {e}")
    
    def set_selected(self, selected):
        """设置选中状态"""
        self.is_selected = selected
        self.update_selection_style()
    
    def set_display_name(self, name):
        """设置显示名称"""
        self.display_name = name
        self.name_label.setText(name)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 检查点击位置
            if self.name_label.geometry().contains(event.pos()):
                # 点击文件名区域不触发卡片选择
                pass
            else:
                # 点击图像区域
                self.card_clicked.emit(self.file_path)
    
    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.LeftButton:
            if self.name_label.geometry().contains(event.pos()):
                # 双击文件名 - 重命名
                self.start_rename()
            else:
                # 双击图像 - 全屏预览
                self.card_double_clicked.emit(self.file_path)
    
    def start_rename(self):
        """开始重命名"""
        if self.is_renaming:
            return
            
        self.is_renaming = True
        
        # 创建重命名对话框
        dialog = QInputDialog(self)
        dialog.setWindowTitle("重命名图像")
        dialog.setLabelText(f"请输入新的名称:\n(当前: {self.display_name})")
        dialog.setTextValue(self.display_name)
        
        # 设置深色主题样式
        dialog.setStyleSheet("""
            QInputDialog {
                background-color: #161617;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QLineEdit {
                background-color: #1a1a1b;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 8px;
                border-radius: 4px;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        
        # 显示对话框
        if dialog.exec_() == QInputDialog.Accepted:
            new_name = dialog.textValue().strip()
            if new_name and new_name != self.display_name:
                self.set_display_name(new_name)
                self.rename_requested.emit(self.file_path, new_name)
        
        self.is_renaming = False
    
    def on_delete_clicked(self):
        """删除按钮点击"""
        # 修复：QMessageBox.question返回的是按钮类型，不是对话框对象
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除图像 '{self.display_name}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.delete_requested.emit(self.file_path)
    
    def on_split_clicked(self):
        """分割按钮点击"""
        self.split_requested.emit(self.file_path)
    
    def on_parse_clicked(self):
        """解析按钮点击"""
        self.parse_requested.emit(self.file_path)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        # 显示操作按钮
        self.delete_btn.setVisible(True)
        self.parse_btn.setVisible(True)
        self.split_btn.setVisible(True)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        # 隐藏操作按钮
        self.delete_btn.setVisible(False)
        self.parse_btn.setVisible(False)
        self.split_btn.setVisible(False)
        super().leaveEvent(event) 