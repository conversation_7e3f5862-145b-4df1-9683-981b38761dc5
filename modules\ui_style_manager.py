"""
UI样式管理器 - 统一管理界面样式
"""


class UIStyleManager:
    """UI样式管理器"""
    
    @staticmethod
    def apply_dark_theme(widget):
        """应用深色主题到指定的widget"""
        stylesheet = UIStyleManager.get_dark_theme_stylesheet()
        widget.setStyleSheet(stylesheet)
    
    @staticmethod
    def get_dark_theme_stylesheet():
        """获取深色主题样式表"""
        return """
        /* 主窗口样式 - 纯黑色背景 + 圆角 */
        QMainWindow {
            background-color: #000000;
            color: #ffffff;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            border-radius: 10px;
        }

        /* 通用容器背景 + 圆角 */
        QWidget {
            background-color: #000000;
            color: #ffffff;
            border-radius: 8px;
        }

        /* 标签页样式 - 优化高度和清晰度 */
        QTabWidget::pane {
            border: 1px solid #3a3a3a;
            background-color: #161617;
            border-radius: 8px;
            top: -1px;
        }

        QTabWidget::tab-bar {
            alignment: left;
        }

        QTabBar::tab {
            background-color: #161617;
            color: #ffffff;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-weight: 500;
            font-size: 12px;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            min-height: 24px;
            max-height: 24px;
            text-align: center;
        }

        QTabBar::tab:selected {
            background-color: #0071e3;
            color: white;
        }

        QTabBar::tab:hover:!selected {
            background-color: #3a3a3a;
        }

        /* 组框样式 - 简洁无边框设计 */
        QGroupBox {
            font-weight: 600;
            border: none;
            border-radius: 0px;
            margin: 0px;
            padding: 0px;
            background-color: transparent;
            color: #ffffff;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 12px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 0px;
            padding: 0px;
            background-color: transparent;
        }

        /* 按钮样式 - 增强圆角 */
        QPushButton {
            background-color: #0071e3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 12px;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
        }

        QPushButton:hover {
            background-color: #005bb5;
        }

        QPushButton:pressed {
            background-color: #004080;
        }

        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #888888;
        }

        /* 输入框样式 - 增强圆角 */
        QLineEdit, QTextEdit, QSpinBox, QComboBox {
            background-color: #161617;
            border: 1px solid #3a3a3a;
            border-radius: 8px;
            padding: 8px;
            color: #ffffff;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 12px;
        }

        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QComboBox:focus {
            border: 1px solid #0071e3;
        }

        /* 下拉框样式 */
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #ffffff;
            margin-right: 5px;
        }

        QComboBox QAbstractItemView {
            background-color: #161617;
            border: 1px solid #3a3a3a;
            selection-background-color: #0071e3;
            color: #ffffff;
            font-size: 12px;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
        }

        /* 复选框样式 */
        QCheckBox {
            color: #ffffff;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 12px;
        }

        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #3a3a3a;
            border-radius: 3px;
            background-color: #161617;
        }

        QCheckBox::indicator:checked {
            background-color: #0071e3;
            border: 1px solid #0071e3;
        }

        QCheckBox::indicator:checked::after {
            content: "✓";
            color: white;
            font-weight: bold;
        }

        /* 表格样式 */
        QTableWidget {
            background-color: #161617;
            alternate-background-color: #1a1a1b;
            gridline-color: #3a3a3a;
            border: 1px solid #3a3a3a;
            border-radius: 6px;
            color: #ffffff;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 12px;
        }

        QTableWidget::item {
            padding: 8px;
            border: none;
            border-right: 1px solid #3a3a3a;
            border-bottom: 1px solid #3a3a3a;
            font-size: 12px;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            text-align: center;
            outline: none;
            /* 🔥 修复：删除强制的color设置，让setForeground()生效 */
        }

        QTableWidget::item:selected {
            background-color: #0071e3;
            color: white;
        }

        /* 表格编辑器样式 */
        QTableWidget QLineEdit {
            background-color: #161617;
            border: 1px solid #0071e3;
            border-radius: 4px;
            padding: 4px;
            color: #ffffff;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 12px;
        }

        QHeaderView::section {
            background-color: #161617;
            color: #ffffff;
            padding: 10px;
            border: 1px solid #3a3a3a;
            font-weight: 600;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 12px;
        }

        /* 列表样式 */
        QListWidget {
            background-color: #161617;
            border: 1px solid #3a3a3a;
            border-radius: 6px;
            color: #ffffff;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 12px;
        }

        QListWidget::item {
            padding: 8px;
            border-bottom: 1px solid #3a3a3a;
        }

        QListWidget::item:selected {
            background-color: #0071e3;
            color: white;
        }

        QListWidget::item:hover {
            background-color: #3a3a3a;
        }

        /* 滚动条样式 */
        QScrollBar:vertical {
            background-color: #161617;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #0071e3;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #005bb5;
        }

        QScrollBar:horizontal {
            background-color: #161617;
            height: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:horizontal {
            background-color: #0071e3;
            border-radius: 6px;
            min-width: 20px;
        }

        QScrollBar::add-line, QScrollBar::sub-line {
            border: none;
            background: none;
        }

        /* 进度条样式 */
        QProgressBar {
            background-color: #161617;
            border: 1px solid #3a3a3a;
            border-radius: 6px;
            text-align: center;
            color: #ffffff;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            font-size: 12px;
        }

        QProgressBar::chunk {
            background-color: #0071e3;
            border-radius: 5px;
        }

        /* 分割器样式 */
        QSplitter::handle {
            background-color: #3a3a3a;
        }

        QSplitter::handle:horizontal {
            width: 4px;
            margin: 2px 0;
        }

        QSplitter::handle:vertical {
            height: 4px;
            margin: 0 2px;
        }

        QSplitter::handle:hover {
            background-color: #0071e3;
        }

        /* 滚动区域样式 - 无边框 */
        QScrollArea {
            background-color: transparent;
            border: none;
            border-radius: 0px;
        }

        /* 框架样式 - 无边框 */
        QFrame {
            background-color: transparent;
            border: none;
            border-radius: 0px;
        }

        /* 状态栏圆角样式 */
        QStatusBar {
            background-color: #161617;
            border: 1px solid #3a3a3a;
            border-radius: 6px;
            color: #ffffff;
        }
        """
    
    @staticmethod
    def get_button_style(button_type="primary"):
        """获取按钮样式"""
        styles = {
            "primary": """
                QPushButton {
                    background-color: #0071e3;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: 500;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
                QPushButton:hover { background-color: #005bb5; }
                QPushButton:pressed { background-color: #004080; }
                QPushButton:disabled { background-color: #3a3a3a; color: #888888; }
            """,
            "success": """
                QPushButton {
                    background-color: #02e87d;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: 500;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
                QPushButton:hover { background-color: #02d46f; }
                QPushButton:pressed { background-color: #02c061; }
            """,
            "warning": """
                QPushButton {
                    background-color: #ff8c00;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: 500;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
                QPushButton:hover { background-color: #e67c00; }
                QPushButton:pressed { background-color: #cc6f00; }
            """,
            "danger": """
                QPushButton {
                    background-color: #d13438;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: 500;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
                QPushButton:hover { background-color: #b92b2f; }
                QPushButton:pressed { background-color: #a12226; }
            """
        }
        return styles.get(button_type, styles["primary"])
