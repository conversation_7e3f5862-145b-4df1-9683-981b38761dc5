#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品链接识别模块
根据商品名称和创建时间识别不同的商品链接
"""

from typing import Dict, List, Any
from datetime import datetime, timedelta
import difflib


class ProductLinkIdentifier:
    """商品链接智能识别器"""

    def __init__(self):
        """
        初始化商品链接识别器

        商品链接识别规则：
        - 商品名称必须100%一致
        - 创建时间必须100%一致
        - 只有完全匹配的SKU才属于同一个商品链接
        """
        pass  # 不再需要阈值参数，使用精确匹配
    
    def identify_product_links(self, skus: List[Dict[str, Any]]) -> Dict[str, List[Dict]]:
        """
        根据商品名称和创建时间识别不同的商品链接

        商品链接识别规则：
        - 商品名称必须100%一致
        - 创建时间必须100%一致
        - 只有完全匹配的SKU才属于同一个商品链接

        Args:
            skus: SKU列表，每个SKU包含商品标题和创建时间

        Returns:
            按商品链接分组的SKU数据: {"link_1": [sku1, sku2], "link_2": [sku3, sku4]}
        """
        if not skus:
            return {}

        # 按商品名称和创建时间的组合进行精确分组
        groups = {}
        link_counter = 1

        for sku in skus:
            # 获取商品名称
            name = (sku.get('name', '') or
                   sku.get('item_name', '') or
                   sku.get('product_title', '') or
                   sku.get('商品标题', ''))

            # 获取创建时间
            created = (sku.get('created', '') or
                      sku.get('modified', '') or
                      sku.get('create_time', '') or
                      sku.get('创建时间', ''))

            # 创建唯一标识符：商品名称 + 创建时间
            link_key = f"{name}|{created}"

            if link_key not in groups:
                groups[link_key] = []
            groups[link_key].append(sku)

        # 转换为最终格式
        final_links = {}
        for link_key, group_skus in groups.items():
            link_id = f"link_{link_counter}"
            final_links[link_id] = group_skus
            link_counter += 1

        return final_links
    
    # 🔥 删除：不再需要相似度分组，改为精确匹配
    # 保留方法以防其他地方调用，但标记为废弃
    def _group_by_title_similarity(self, skus: List[Dict]) -> Dict[str, List[Dict]]:
        """
        废弃方法：原来根据商品标题相似度分组
        现在改为精确匹配，此方法保留以防兼容性问题
        """
        print("⚠️ 警告：_group_by_title_similarity方法已废弃，请使用新的精确匹配逻辑")
        return {}
    
    # 🔥 删除：不再需要相似度计算，改为精确匹配
    # 保留方法以防其他地方调用，但标记为废弃
    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """
        废弃方法：原来计算两个标题的相似度
        现在改为精确匹配，此方法保留以防兼容性问题
        """
        print("⚠️ 警告：_calculate_title_similarity方法已废弃，请使用精确匹配逻辑")
        return 1.0 if title1 == title2 else 0.0
    
    # 🔥 删除：不再需要时间窗口分组，改为精确匹配
    # 保留方法以防其他地方调用，但标记为废弃
    def _split_by_time_window(self, skus: List[Dict]) -> List[List[Dict]]:
        """
        废弃方法：原来根据创建时间窗口分组
        现在改为精确匹配，此方法保留以防兼容性问题
        """
        print("⚠️ 警告：_split_by_time_window方法已废弃，请使用新的精确匹配逻辑")
        return [skus] if skus else []
    
    def _parse_create_time(self, sku: Dict) -> datetime:
        """
        解析SKU的创建时间

        Args:
            sku: SKU数据

        Returns:
            创建时间的datetime对象
        """
        # 🔥 修复：支持更多字段名变体
        create_time_str = (sku.get('create_time', '') or
                          sku.get('创建时间', '') or
                          sku.get('created', '') or
                          sku.get('modified', ''))
        if not create_time_str:
            return datetime.now()  # 默认当前时间

        try:
            # 尝试多种时间格式
            time_formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d',
                '%m/%d/%Y',
                '%d/%m/%Y'
            ]

            for fmt in time_formats:
                try:
                    return datetime.strptime(create_time_str, fmt)
                except ValueError:
                    continue

            # 如果所有格式都失败，返回当前时间
            return datetime.now()

        except Exception:
            return datetime.now()


def test_product_link_identifier():
    """测试商品链接识别器"""
    identifier = ProductLinkIdentifier()
    
    # 测试数据：模拟GD340-9402的情况
    test_skus = [
        {
            "sku_id": "GD340-9402-红色-S",
            "product_title": "时尚女装连衣裙 春季新款",
            "create_time": "2024-01-15 10:00:00"
        },
        {
            "sku_id": "GD340-9402-红色-M", 
            "product_title": "时尚女装连衣裙 春季新款",
            "create_time": "2024-01-15 10:05:00"
        },
        {
            "sku_id": "GD340-9402-蓝色-S",
            "product_title": "时尚女装连衣裙 春季新款 优质面料",
            "create_time": "2024-01-25 14:00:00"
        },
        {
            "sku_id": "GD340-9402-蓝色-M",
            "product_title": "时尚女装连衣裙 春季新款 优质面料", 
            "create_time": "2024-01-25 14:10:00"
        }
    ]
    
    # 识别商品链接
    product_links = identifier.identify_product_links(test_skus)
    
    print("🔍 商品链接识别结果:")
    for link_id, link_skus in product_links.items():
        print(f"\n📦 {link_id}:")
        for sku in link_skus:
            print(f"  - {sku['sku_id']}: {sku['product_title']} ({sku['create_time']})")
    
    print(f"\n✅ 识别出 {len(product_links)} 个商品链接")
    return product_links


if __name__ == "__main__":
    test_product_link_identifier()
