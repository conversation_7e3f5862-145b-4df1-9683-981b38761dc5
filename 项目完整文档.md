# 智能票据处理系统 - 完整项目文档

一个基于AI和PyQt5开发的智能票据处理系统，能够自动识别和解析票据图像，提取商品信息，并与ERP系统集成进行成本价管理和利润分析。

## 🌟 核心功能

### 🤖 AI智能解析
- **多模型支持**：支持doubao-1.5-vision-lite等主流视觉AI模型
- **智能识别**：自动识别票据中的商品信息（款号、颜色规格、数量、单价等）
- **格式兼容**：支持多种票据格式和图像类型（JPG、PNG、BMP等）
- **图像分割**：智能图像分割功能，处理长票据和复杂布局
- **数据标准化**：结构化数据提取和格式标准化处理

### 🔍 ERP系统集成
- **实时查询**：实时查询ERP商品信息和价格数据
- **智能匹配**：基于编辑距离的智能SKU匹配算法
- **批量操作**：支持批量成本价更新和库存查询
- **状态管理**：完整的查询状态跟踪和结果缓存
- **认证管理**：自动Cookie管理和认证状态检查

### 💰 成本价管理
- **利润计算**：自动计算商品利润和毛利率
- **颜色确认**：可视化颜色规格确认面板
- **价格对比**：实时显示ERP售价与票据成本价对比
- **状态显示**：价格上涨红色、价格下降绿色的直观显示
- **批量更新**：支持选择性批量更新ERP系统成本价

### 🎨 现代化界面
- **深色主题**：基于PyQt5的美观界面设计，护眼黑色主题
- **模块化布局**：标签页式布局，功能区域清晰分离
- **图像管理**：完整的图像预览、缩略图和全屏显示功能
- **进度反馈**：统一的进度条显示，实时操作状态反馈
- **响应式设计**：自适应布局和流畅的交互体验

### 🔒 数据状态管理
- **状态隔离**：完整的表格数据状态隔离机制
- **持久化**：多文件处理时的状态持久化和恢复
- **精确匹配**：基于商品唯一标识的精确匹配算法
- **污染防护**：避免数据污染的核心技术方案

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/11
- **Python版本**: 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **网络**: 稳定的互联网连接（AI服务和ERP查询）

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd 自动填写成本价
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置系统**
   - 编辑 `user_config.json` 配置AI API密钥
   - 配置ERP系统访问参数
   - 设置供应商信息

4. **启动程序**
```bash
python main.py
# 或双击 启动程序.bat
```

## 📂 项目架构

### 根目录结构
```
智能票据处理系统/
├── 📄 main.py                          # 程序启动入口
├── 🚀 启动程序.bat                     # Windows快速启动脚本
├── 📋 requirements.txt                 # Python依赖包列表
├── 📄 项目完整文档.md                  # 完整项目文档（本文件）
│
├── 📂 modules/                         # 核心功能模块目录
│   ├── 🎨 pyqt5_main_gui.py           # 主界面入口
│   ├── 🤖 ai_processor.py             # AI解析处理核心
│   ├── 🔍 erp_integration.py          # ERP系统集成接口
│   ├── 🎯 sku_matcher.py              # 智能SKU匹配算法
│   ├── ⚙️ config_manager.py           # 配置管理器
│   ├── 📁 file_manager.py             # 文件管理器
│   ├── 🖼️ image_splitter.py           # 图像分割处理
│   ├── 🏢 supplier_manager.py         # 供应商管理
│   ├── 🎨 color_confirm_panel.py      # 颜色确认面板
│   ├── 📊 table_manager.py            # 表格数据管理
│   ├── 📈 return_rate_calculator.py   # 退货率计算器
│   ├── 🔗 product_link_identifier.py  # 商品链接识别器
│   ├── 🎨 ui_style_manager.py         # UI样式管理
│   ├── 📋 ui_constants.py             # UI常量定义
│   │
│   ├── 📂 gui/                        # GUI组件模块
│   │   ├── 🎮 main_gui_controller.py  # 主界面控制器
│   │   ├── 🖼️ image_manager.py        # 图像管理组件
│   │   ├── 📊 table_ui_manager.py     # 表格UI管理
│   │   ├── ⚙️ config_ui_manager.py    # 配置界面管理
│   │   ├── 🔍 erp_ui_manager.py       # ERP界面管理
│   │   └── 🎨 image_card_widget.py    # 图像卡片组件
│   │
│   ├── 📂 threads/                    # 线程处理模块
│   │   ├── 🤖 ai_processing_thread.py # AI处理线程
│   │   ├── 🔍 erp_query_thread.py     # ERP查询线程
│   │   ├── 💰 cost_update_thread.py   # 成本更新线程
│   │   └── 📝 thread_safe_logger.py   # 线程安全日志
│   │
│   ├── 📂 cookies/                    # Cookie管理模块
│   │   ├── 🍪 manager.py              # Cookie管理器
│   │   ├── 🔍 parser.py               # Cookie解析器
│   │   ├── ✅ validator.py            # Cookie验证器
│   │   └── ❌ exceptions.py           # 异常处理
│   │
│   └── 📂 utils/                      # 工具函数模块
│       └── 📝 logger.py               # 日志工具
│
├── 📄 user_config.json                 # 用户个人配置文件
├── 📄 suppliers.json                   # 供应商数据库
├── 📄 file_manager.json                # 文件管理配置
└── 📄 latest_cookies.json              # ERP系统访问Cookie
```

## 🔧 核心模块详解

### 1. 主界面模块 (`pyqt5_main_gui.py`)
- **职责**：程序主入口，界面初始化和事件分发
- **特点**：轻量化设计，主要负责界面框架搭建

### 2. 表格管理器 (`table_manager.py`)
- **职责**：表格数据的CRUD操作、状态管理和数据持久化
- **核心功能**：状态隔离机制，防止多文件数据污染

### 3. ERP集成模块 (`erp_integration.py`)
- **职责**：ERP系统API调用、数据查询和更新
- **支持功能**：商品查询、成本价更新、库存状态查询

### 4. AI处理器 (`ai_processor.py`)
- **职责**：票据图像识别、商品信息提取
- **支持模型**：doubao-1.5-vision-lite 等主流视觉模型

### 5. 退货率计算器 (`return_rate_calculator.py`)
- **职责**：商品退货率计算和显示
- **核心逻辑**：按商品链接级别计算，支持多链接场景

### 6. 商品链接识别器 (`product_link_identifier.py`)
- **职责**：识别同一商品的不同SKU，进行商品链接分组
- **匹配规则**：商品名称和创建时间100%精确匹配

## 🚨 核心技术难点解决方案

### 1. 表格数据污染问题

**问题背景**：
- 多文件处理时状态污染
- 切换表格时状态丢失
- 不同文件数据互相干扰

**解决方案**：
```python
# 状态隔离机制
erp_states_cache = {
    "file_path_1": {
        "sku_based_erp_state": {
            "商品A|红色XL": {
                "status": "📈 上涨 ¥5",
                "confirmed": True,
                "profit_text": "¥20"
            }
        }
    }
}
```

### 2. 商品链接识别优化

**问题背景**：
- 同一商品的不同颜色规格需要统一退货率
- 不同商品链接需要分别计算退货率

**解决方案**：
- 使用商品名称和创建时间的精确匹配
- 按商品链接级别计算退货率
- 支持单链接多颜色和多链接多颜色场景

### 3. ERP Token失效处理

**问题背景**：
- ERP系统Token定期失效
- 需要自动检测和重新认证

**解决方案**：
- 实现自动Token检测机制
- 提供手动和自动Cookie更新功能
- 完整的认证状态管理

## 📊 退货率计算逻辑

### 计算规则
1. **商品链接识别**：商品名称和创建时间100%一致
2. **数据汇总**：按商品链接汇总所有SKU的发货和退货数据
3. **退货率计算**：退货率 = 总退货数量 ÷ 总发货数量 × 100%
4. **显示规则**：
   - 同一商品链接的所有颜色显示相同退货率
   - 不同商品链接分别显示各自的退货率
   - 只显示30天退货率，格式为整数百分比

### 颜色标识
- **绿色**：退货率 < 20%（低风险）
- **橙色**：20% ≤ 退货率 < 50%（中等风险）
- **红色**：退货率 ≥ 50%（高风险）

## 🔌 ERP API接口文档

### 认证接口
```
POST /api/auth/login
Content-Type: application/json

{
    "username": "用户名",
    "password": "密码"
}
```

### 商品查询接口
```
GET /api/products/search?keyword={商品关键词}
Authorization: Bearer {token}
```

### 成本价更新接口
```
PUT /api/products/{product_id}/cost
Authorization: Bearer {token}
Content-Type: application/json

{
    "cost_price": 新成本价,
    "update_reason": "更新原因"
}
```

## ⚙️ 配置说明

### user_config.json
```json
{
    "ai_config": {
        "api_key": "你的AI API密钥",
        "base_url": "https://ark.cn-beijing.volces.com/api/v3",
        "model_name": "doubao-1.5-vision-lite-250315"
    },
    "erp_config": {
        "base_url": "ERP系统地址",
        "timeout": 30
    }
}
```

### suppliers.json
```json
{
    "suppliers": [
        {
            "name": "供应商名称",
            "code": "供应商代码",
            "contact": "联系方式"
        }
    ]
}
```

## 🚀 使用指南

### 1. 基本操作流程
1. 启动程序
2. 上传票据图像
3. AI自动解析商品信息
4. ERP系统查询匹配
5. 确认颜色规格
6. 批量更新成本价

### 2. 高级功能
- **图像分割**：处理长票据，自定义分割区域
- **批量处理**：同时处理多个票据文件
- **状态管理**：保存和恢复处理状态
- **数据导出**：导出处理结果到Excel

## 🔧 开发指南

### 环境搭建
```bash
# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 代码规范
- 使用Python 3.8+语法
- 遵循PEP 8代码规范
- 添加类型提示
- 完善文档字符串

### 测试
- 单元测试覆盖核心功能
- 集成测试验证完整流程
- 性能测试确保响应速度

## 📝 更新日志

### v2.0.0 (2024-07-09)
- ✅ 修复商品链接识别逻辑，改为精确匹配
- ✅ 优化退货率计算，支持商品链接级别计算
- ✅ 完善状态隔离机制，解决数据污染问题
- ✅ 重构项目结构，清理冗余文件
- ✅ 整合项目文档，提供完整技术说明

### v1.x.x
- 基础功能实现
- AI解析集成
- ERP系统对接
- 界面优化

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。

---

**开发团队**：一介哥专用AI助手  
**最后更新**：2024年7月9日
