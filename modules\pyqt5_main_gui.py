"""
PyQt5主界面模块 - 智能票据处理系统 (重构版本)
采用现代化设计，支持标签页布局和黑色主题

本文件已重构为模块化结构，提高代码可维护性
原始6557行代码已拆分为7个专门的功能模块：

1. main_gui_controller.py - 主界面控制器（核心窗口和标签页管理）
2. image_manager.py - 图像管理器（图像预览、缩略图、全屏显示）
3. table_ui_manager.py - 表格界面管理器（结果表格、数据显示）
4. ai_processor_ui.py - AI处理界面（AI解析相关界面逻辑）
5. config_ui_manager.py - 配置界面管理器（所有配置页面）
6. erp_ui_manager.py - ERP界面管理器（ERP查询和更新界面）
7. event_handlers.py - 事件处理器（用户交互事件处理）

重构优势：
- 代码可读性大幅提升
- 便于功能定位和修改
- 模块职责单一且清晰
- 提高代码复用性
- 降低维护成本

重构前后对比：
- 重构前：1个文件，6557行代码，难以维护
- 重构后：7个模块，代码结构清晰，便于开发

备份信息：
- 原始文件已备份到: beifen/2024-12-19_GUI重构前完整备份_pyqt5_main_gui.py
- 重构说明文档: beifen/2024-12-19_GUI重构说明.md
"""

# 使用重构后的GUI组件
from modules.gui import ModernTicketProcessorGUI

# 兼容性导出，确保现有代码仍能正常工作
__all__ = ['ModernTicketProcessorGUI']

def main():
    """主函数"""
    import sys
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    
    # 设置高DPI支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("智能票据处理系统")
    app.setApplicationVersion("2.0 (重构版)")
    
    # 创建并显示主窗口
    window = ModernTicketProcessorGUI()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 