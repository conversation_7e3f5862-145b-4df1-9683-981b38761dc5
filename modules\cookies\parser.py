"""
Cookies解析器模块 - 负责解析多种格式的cookies文本
"""

import json
import re
import urllib.parse
from typing import Dict, Tu<PERSON>, Optional
from .exceptions import CookiesParseError


class CookiesParser:
    """Cookies解析器 - 支持多种格式的智能解析"""
    
    def __init__(self):
        self.supported_formats = [
            'browser',    # 浏览器开发者工具格式
            'json',       # JSON格式
            'keyvalue',   # 键值对格式
            'single'      # 单个cookie格式
        ]
    
    def parse(self, text: str) -> Dict[str, str]:
        """
        智能解析cookies文本
        
        Args:
            text: 待解析的cookies文本
            
        Returns:
            解析后的cookies字典
            
        Raises:
            CookiesParseError: 解析失败时抛出
        """
        if not text or not text.strip():
            raise CookiesParseError("输入的cookies文本为空", "请输入有效的cookies内容")
        
        # 清理输入文本
        cleaned_text = self._clean_input(text)
        
        # 检测格式并解析
        format_type = self._detect_format(cleaned_text)
        
        try:
            if format_type == 'json':
                return self._parse_json_format(cleaned_text)
            elif format_type == 'browser':
                return self._parse_browser_format(cleaned_text)
            elif format_type == 'keyvalue':
                return self._parse_keyvalue_format(cleaned_text)
            elif format_type == 'single':
                return self._parse_single_cookie(cleaned_text)
            else:
                raise CookiesParseError(
                    "无法识别cookies格式",
                    "支持的格式：浏览器格式(key=value;)、JSON格式({})、键值对格式(key: value)"
                )
        except Exception as e:
            if isinstance(e, CookiesParseError):
                raise
            else:
                raise CookiesParseError(f"解析过程中发生错误: {str(e)}")
    
    def _clean_input(self, text: str) -> str:
        """清理输入文本，去除多余的空白字符和格式"""
        # 去除首尾空白
        text = text.strip()
        
        # 去除可能的富文本格式标记
        text = re.sub(r'<[^>]+>', '', text)
        
        # 标准化换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 去除多余的空行
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        return '\n'.join(lines) if len(lines) > 1 else text
    
    def _detect_format(self, text: str) -> str:
        """
        检测cookies格式
        
        Args:
            text: 清理后的cookies文本
            
        Returns:
            格式类型: 'json', 'browser', 'keyvalue', 'single'
        """
        text = text.strip()
        
        # 检测JSON格式
        if text.startswith('{') and text.endswith('}'):
            try:
                json.loads(text)
                return 'json'
            except json.JSONDecodeError:
                pass
        
        # 检测浏览器格式（包含分号分隔）
        if ';' in text and '=' in text:
            # 检查是否符合浏览器cookies格式
            parts = text.split(';')
            valid_parts = 0
            for part in parts:
                part = part.strip()
                if '=' in part:
                    valid_parts += 1
            
            if valid_parts >= len(parts) * 0.8:  # 80%的部分都是有效的key=value格式
                return 'browser'
        
        # 检测键值对格式（多行，包含冒号）
        if '\n' in text:
            lines = text.split('\n')
            valid_lines = 0
            for line in lines:
                line = line.strip()
                if ':' in line or '=' in line:
                    valid_lines += 1
            
            if valid_lines >= len(lines) * 0.8:  # 80%的行都是有效的键值对
                return 'keyvalue'
        
        # 检测单个cookie格式
        if '=' in text and ';' not in text and '\n' not in text:
            return 'single'
        
        # 如果都不匹配，尝试作为浏览器格式处理
        return 'browser'
    
    def _parse_json_format(self, text: str) -> Dict[str, str]:
        """解析JSON格式的cookies"""
        try:
            data = json.loads(text)
            if not isinstance(data, dict):
                raise CookiesParseError("JSON格式错误", "cookies应该是一个对象格式: {\"key\": \"value\"}")
            
            # 转换所有值为字符串
            result = {}
            for key, value in data.items():
                if not isinstance(key, str):
                    raise CookiesParseError(f"无效的cookie名称: {key}", "cookie名称必须是字符串")
                result[key] = str(value)
            
            return result
            
        except json.JSONDecodeError as e:
            raise CookiesParseError(f"JSON格式解析失败: {str(e)}", "请检查JSON格式是否正确")
    
    def _parse_browser_format(self, text: str) -> Dict[str, str]:
        """解析浏览器开发者工具格式的cookies"""
        result = {}
        
        # 分割cookies（支持分号和换行符分割）
        if ';' in text:
            parts = text.split(';')
        else:
            parts = text.split('\n')
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
                
            if '=' not in part:
                continue
                
            try:
                # 分割键值对（只分割第一个等号）
                key, value = part.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # 处理可能的引号
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]
                
                # 处理URL编码
                try:
                    decoded_value = urllib.parse.unquote(value)
                    if decoded_value != value:
                        value = decoded_value
                except:
                    pass  # 如果解码失败，保持原值
                
                if key:  # 确保key不为空
                    result[key] = value
                    
            except ValueError:
                continue  # 跳过无法解析的部分
        
        if not result:
            raise CookiesParseError("未找到有效的cookie数据", "浏览器格式应为: key1=value1; key2=value2")
        
        return result
    
    def _parse_keyvalue_format(self, text: str) -> Dict[str, str]:
        """解析键值对格式的cookies"""
        result = {}
        
        lines = text.split('\n')
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            # 支持冒号和等号分隔
            separator = ':' if ':' in line else '='
            if separator not in line:
                continue
            
            try:
                key, value = line.split(separator, 1)
                key = key.strip()
                value = value.strip()
                
                # 处理可能的引号
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]
                
                if key:  # 确保key不为空
                    result[key] = value
                    
            except ValueError:
                continue  # 跳过无法解析的行
        
        if not result:
            raise CookiesParseError("未找到有效的cookie数据", "键值对格式应为: key1: value1\\nkey2: value2")
        
        return result
    
    def _parse_single_cookie(self, text: str) -> Dict[str, str]:
        """解析单个cookie"""
        text = text.strip()
        
        if '=' not in text:
            raise CookiesParseError("单个cookie格式错误", "应为: key=value")
        
        try:
            key, value = text.split('=', 1)
            key = key.strip()
            value = value.strip()
            
            if not key:
                raise CookiesParseError("cookie名称不能为空", "应为: key=value")
            
            return {key: value}
            
        except ValueError:
            raise CookiesParseError("单个cookie解析失败", "应为: key=value")
    
    def get_format_examples(self) -> Dict[str, str]:
        """获取支持格式的示例"""
        return {
            'browser': '_sid18707109=ABC123; u_id=18707109; u_co_id=13881863; isLogin=true',
            'json': '{"_sid18707109": "ABC123", "u_id": "18707109", "u_co_id": "13881863"}',
            'keyvalue': '_sid18707109: ABC123\nu_id: 18707109\nu_co_id: 13881863',
            'single': '_sid18707109=ABC123'
        }
