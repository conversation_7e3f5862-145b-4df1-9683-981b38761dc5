#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像分割线Overlay组件
在预览窗口内直接添加可拖拽的分割线，提供直观的图像分割功能
"""

import os
from typing import List, Tuple, Optional
from PyQt5.QtWidgets import QWidget, QPushButton, QHBoxLayout, QVBoxLayout
from PyQt5.QtCore import Qt, QPoint, pyqtSignal, QRect, QTimer
from PyQt5.QtGui import QPainter, QPen, QColor, QCursor, QMouseEvent, QPaintEvent, QResizeEvent
from PIL import Image


class DraggableSplitLine(QWidget):
    """可拖拽的分割线组件"""

    position_changed = pyqtSignal(int, int)  # 发送 (line_id, new_y_position)

    def __init__(self, line_id: int, y_position: int, image_height: int, scale_factor: float = 1.0, parent=None):
        super().__init__(parent)
        self.line_id = line_id
        self.y_position = y_position  # 相对于原图的Y坐标
        self.image_height = image_height
        self.scale_factor = scale_factor  # 图像缩放因子
        self.is_dragging = False
        self.drag_start_y = 0
        self.drag_start_position = 0  # 拖拽开始时的原图位置
        self.min_distance = 30  # 分割线之间的最小距离
        self.is_hovered = False
        
        # 设置基本属性
        self.setFixedHeight(3)
        self.setCursor(QCursor(Qt.SizeVerCursor))
        self.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        
        # 启用鼠标跟踪以检测悬停
        self.setMouseTracking(True)
        
    def paintEvent(self, event: QPaintEvent):
        """绘制分割线"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 根据状态设置颜色和透明度
        if self.is_hovered or self.is_dragging:
            color = QColor(255, 68, 68, 220)  # 悬停时更亮
            pen_width = 4
        else:
            color = QColor(255, 68, 68, 180)  # 默认半透明红色
            pen_width = 3
            
        pen = QPen(color, pen_width)
        painter.setPen(pen)
        
        # 绘制分割线
        painter.drawLine(0, self.height() // 2, self.width(), self.height() // 2)
        
        # 绘制拖拽手柄（中间的小圆点）
        handle_color = QColor(255, 255, 255, 200)
        painter.setBrush(handle_color)
        painter.setPen(QPen(color, 2))
        center_x = self.width() // 2
        center_y = self.height() // 2
        painter.drawEllipse(center_x - 4, center_y - 4, 8, 8)
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self.update()
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        if not self.is_dragging:
            self.is_hovered = False
            self.update()
        super().leaveEvent(event)
        
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = True
            self.drag_start_y = event.globalY()
            self.drag_start_position = self.y_position  # 记录拖拽开始时的原图位置
            self.is_hovered = True
            self.update()

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self.is_dragging:
            # 计算鼠标移动的像素距离
            delta_y_pixels = event.globalY() - self.drag_start_y

            # 将像素距离转换为原图坐标距离（考虑缩放因子）
            delta_y_original = delta_y_pixels / self.scale_factor

            # 计算新的原图位置
            new_y = self.drag_start_position + delta_y_original

            # 限制在图像范围内
            new_y = max(self.min_distance, min(new_y, self.image_height - self.min_distance))

            if abs(new_y - self.y_position) > 1:  # 避免微小抖动
                self.y_position = new_y
                self.position_changed.emit(self.line_id, int(new_y))

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.update()

    def update_scale_factor(self, scale_factor: float):
        """更新缩放因子"""
        self.scale_factor = scale_factor


class ImageSplitOverlay(QWidget):
    """图像分割Overlay组件 - 覆盖在图像预览上的透明层"""
    
    # 信号定义
    split_completed = pyqtSignal(list)  # 分割完成信号
    split_mode_changed = pyqtSignal(bool)  # 分割模式改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.image_path = None
        self.original_image_size = (0, 0)  # 原图尺寸
        self.display_image_size = (0, 0)   # 显示尺寸
        self.image_scale_factor = 1.0
        self.split_lines = []  # 存储分割线对象
        self.split_positions = []  # 存储分割位置 (相对于原图的y坐标)
        self.is_split_mode = False

        # 设置透明背景
        self.setAttribute(Qt.WA_TransparentForMouseEvents, True)
        self.setStyleSheet("background-color: transparent;")
        

        
    def enter_split_mode(self, image_path: str, image_size: Tuple[int, int], display_size: Tuple[int, int]):
        """进入分割模式"""
        self.image_path = image_path
        self.original_image_size = image_size
        self.display_image_size = display_size
        self.image_scale_factor = display_size[1] / image_size[1] if image_size[1] > 0 else 1.0
        
        self.is_split_mode = True
        self.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        
        # 初始化默认分割线
        self.initialize_default_split_lines()
        
        # 发送模式改变信号
        self.split_mode_changed.emit(True)
        
    def exit_split_mode(self):
        """退出分割模式"""
        self.is_split_mode = False
        self.setAttribute(Qt.WA_TransparentForMouseEvents, True)
        
        # 清除分割线
        self.clear_split_lines()
        
        # 发送模式改变信号
        self.split_mode_changed.emit(False)
        
    def initialize_default_split_lines(self):
        """初始化默认分割线（三等分）"""
        if not self.original_image_size[1]:
            return
            
        # 清除现有分割线
        self.clear_split_lines()
        
        # 计算分割位置（基于原图尺寸）
        image_height = self.original_image_size[1]
        split_y1 = image_height // 3
        split_y2 = image_height * 2 // 3
        
        # 添加两条分割线
        self.add_split_line_at_position(split_y1)
        self.add_split_line_at_position(split_y2)
        
    def add_split_line_at_position(self, y_position: int):
        """在指定位置添加分割线"""
        if not self.original_image_size[1]:
            return
            
        # 转换为显示坐标
        display_y = int(y_position * self.image_scale_factor)
        
        # 创建分割线
        line_id = len(self.split_lines)
        split_line = DraggableSplitLine(
            line_id,
            y_position,  # 存储原图坐标
            self.original_image_size[1],
            self.image_scale_factor,  # 传递缩放因子
            self
        )
        
        # 设置位置和大小
        split_line.setGeometry(0, display_y, self.width(), 3)
        split_line.position_changed.connect(self.on_split_line_moved)
        split_line.show()
        
        self.split_lines.append(split_line)
        self.update_split_positions()
        
    def add_split_line(self):
        """添加新的分割线"""
        if not self.original_image_size[1]:
            return
            
        # 在图像中间添加新分割线
        image_height = self.original_image_size[1]
        new_y = image_height // 2
        
        # 如果已有分割线，尝试找到合适的位置
        if self.split_positions:
            sorted_positions = sorted(self.split_positions)
            
            # 尝试在第一条线之前
            if sorted_positions[0] > 80:
                new_y = sorted_positions[0] // 2
            # 尝试在最后一条线之后
            elif image_height - sorted_positions[-1] > 80:
                new_y = (sorted_positions[-1] + image_height) // 2
            # 在中间找最大的空隙
            else:
                max_gap = 0
                best_position = new_y
                for i in range(len(sorted_positions) - 1):
                    gap = sorted_positions[i + 1] - sorted_positions[i]
                    if gap > max_gap and gap > 80:
                        max_gap = gap
                        best_position = (sorted_positions[i] + sorted_positions[i + 1]) // 2
                new_y = best_position
                
        self.add_split_line_at_position(new_y)
        
    def remove_last_split_line(self):
        """删除最后一条分割线"""
        if self.split_lines:
            line = self.split_lines.pop()
            line.deleteLater()
            self.update_split_positions()
            
    def clear_split_lines(self):
        """清除所有分割线"""
        for line in self.split_lines:
            line.deleteLater()
        self.split_lines.clear()
        self.split_positions.clear()
        
    def on_split_line_moved(self, line_id: int, new_y_position: int):
        """分割线移动事件处理"""
        # 更新分割线位置（显示坐标）
        if 0 <= line_id < len(self.split_lines):
            split_line = self.split_lines[line_id]
            display_y = int(new_y_position * self.image_scale_factor)
            split_line.move(0, display_y)
            
        self.update_split_positions()
        
    def update_split_positions(self):
        """更新分割位置列表"""
        self.split_positions = []
        for line in self.split_lines:
            self.split_positions.append(line.y_position)
            
    def update_display_size(self, display_size: Tuple[int, int]):
        """更新显示尺寸并同步分割线位置"""
        if not self.original_image_size[1]:
            return
            
        self.display_image_size = display_size
        self.image_scale_factor = display_size[1] / self.original_image_size[1]
        
        # 更新所有分割线的显示位置和缩放因子
        for line in self.split_lines:
            if line and not line.isHidden():
                # 更新缩放因子
                line.update_scale_factor(self.image_scale_factor)
                # 更新显示位置
                display_y = int(line.y_position * self.image_scale_factor)
                line.move(0, display_y)
                line.setFixedWidth(self.width())

    def resizeEvent(self, event: QResizeEvent):
        """窗口大小改变事件"""
        super().resizeEvent(event)

        # 更新分割线宽度
        for line in self.split_lines:
            line.setFixedWidth(self.width())
            
    def confirm_split(self):
        """确认分割"""
        if not self.image_path or not self.split_positions:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "请先添加分割线")
            return
            
        try:
            # 执行图像分割
            split_files = self.perform_image_split()
            
            if split_files:
                # 发送分割完成信号
                self.split_completed.emit(split_files)
                # 退出分割模式
                self.exit_split_mode()
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "分割失败，未生成文件")
                
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"分割失败: {str(e)}")
            
    def perform_image_split(self) -> List[str]:
        """执行图像分割并返回分割后的文件路径列表"""
        if not self.image_path or not self.split_positions:
            return []
            
        try:
            # 打开原图
            img = Image.open(self.image_path)
            width, height = img.size
            
            # 创建输出目录
            output_dir = "temp_segments"
            os.makedirs(output_dir, exist_ok=True)
            
            # 排序分割位置
            sorted_positions = sorted(self.split_positions)
            
            # 构建分割区域
            split_regions = []
            y_start = 0
            
            for y_pos in sorted_positions:
                split_regions.append((y_start, y_pos))
                y_start = y_pos
                
            # 添加最后一个区域
            split_regions.append((y_start, height))
            
            # 执行分割并保存
            split_files = []
            base_name = os.path.splitext(os.path.basename(self.image_path))[0]
            file_extension = os.path.splitext(self.image_path)[1] or '.jpg'

            for i, (y_start, y_end) in enumerate(split_regions):
                # 裁剪图像
                segment = img.crop((0, y_start, width, y_end))

                # 生成文件名：原文件名_1、2、3
                segment_filename = f"{base_name}_{i+1}{file_extension}"
                segment_path = os.path.join(output_dir, segment_filename)

                # 保存图像
                segment.save(segment_path, 'JPEG', quality=95)
                split_files.append(segment_path)
                
            return split_files
            
        except Exception as e:
            raise Exception(f"图像分割失败: {str(e)}")
