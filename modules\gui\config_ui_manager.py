"""
配置界面管理器 - 负责所有配置页面
重构自原始pyqt5_main_gui.py的配置部分
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import os

from modules.ui_constants import UIConstants


class ConfigUIManager:
    """配置界面管理器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        
    def create_config_tab(self):
        """创建配置标签页"""
        config_tab = QWidget()
        self.main_window.tab_widget.addTab(config_tab, "⚙️ 配置设置")

        config_layout = QHBoxLayout(config_tab)

        # 创建配置分割器
        config_splitter = QSplitter(Qt.Horizontal)
        config_layout.addWidget(config_splitter)

        # 左侧配置选项
        self.create_config_left_panel(config_splitter)

        # 右侧配置详情
        self.create_config_right_panel(config_splitter)

        # 设置分割器比例
        config_splitter.setSizes([300, 800])

    def create_config_left_panel(self, parent):
        """创建配置左侧面板"""
        left_widget = QWidget()
        parent.addWidget(left_widget)

        left_layout = QVBoxLayout(left_widget)

        # 配置分类列表
        config_label = QLabel("配置分类")
        config_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        config_label.setStyleSheet("color: #ffffff; margin-bottom: 10px;")
        left_layout.addWidget(config_label)

        self.config_list = QListWidget()
        self.config_list.addItems([
            "🤖 AI配置",
            "📝 提示词设置",
            "🏢 供应商管理",
            "🎨 界面设置",
            "🍪 Cookies管理",
            "🧠 智能记忆"  # 新增智能记忆选项
        ])
        self.config_list.currentRowChanged.connect(self.on_config_category_changed)
        self.config_list.setStyleSheet("""
            QListWidget {
                background-color: #161617;
                border: 1px solid #3a3a3a;
                border-radius: 8px;
                padding: 5px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #ffffff;
            }
            QListWidget::item {
                padding: 10px;
                border-radius: 6px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #2a2a2a;
            }
        """)
        left_layout.addWidget(self.config_list)

        # 配置操作按钮
        config_ops_layout = QVBoxLayout()

        self.save_config_btn = QPushButton("💾 保存配置")
        self.save_config_btn.clicked.connect(self.save_configuration)
        config_ops_layout.addWidget(self.save_config_btn)

        self.load_config_btn = QPushButton("📂 加载配置")
        self.load_config_btn.clicked.connect(self.load_configuration)
        config_ops_layout.addWidget(self.load_config_btn)

        self.reset_config_btn = QPushButton("🔄 重置配置")
        self.reset_config_btn.clicked.connect(self.reset_configuration)
        config_ops_layout.addWidget(self.reset_config_btn)

        # 统一按钮样式
        button_style = """
            QPushButton {
                background-color: #0071e3;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei';
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #005bb5;
            }
            QPushButton:pressed {
                background-color: #004080;
            }
        """

        for btn in [self.save_config_btn, self.load_config_btn, self.reset_config_btn]:
            btn.setStyleSheet(button_style)

        left_layout.addLayout(config_ops_layout)
        left_layout.addStretch()

    def create_config_right_panel(self, parent):
        """创建配置右侧面板"""
        right_widget = QWidget()
        parent.addWidget(right_widget)

        right_layout = QVBoxLayout(right_widget)

        # 配置详情区域
        self.config_stack = QWidget()
        self.config_stack_layout = QVBoxLayout(self.config_stack)
        right_layout.addWidget(self.config_stack)

        # 创建各个配置面板
        self.create_ai_config_panel()
        self.create_prompt_config_panel()
        self.create_supplier_config_panel()
        self.create_ui_config_panel()
        self.create_cookies_config_panel()  # 新增Cookies配置面板
        self.create_smart_memory_config_panel()  # 新增智能记忆配置面板

        # 默认显示AI配置
        self.current_config_panel = "ai"
        self.show_config_panel("ai")

    def create_ai_config_panel(self):
        """创建AI配置面板"""
        self.ai_config_panel = QWidget()
        self.ai_config_panel.setVisible(False)
        self.ai_config_panel.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
                color: #ffffff;
            }
        """)
        self.config_stack_layout.addWidget(self.ai_config_panel)

        ai_layout = QVBoxLayout(self.ai_config_panel)
        ai_layout.setContentsMargins(0, 0, 0, 0)
        ai_layout.setSpacing(12)

        # API配置表单
        api_form = QWidget()
        ai_layout.addWidget(api_form)
        ai_form_layout = QFormLayout(api_form)

        # 统一的输入框样式
        input_style = """
            QLineEdit {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 6px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """

        label_style = """
            QLabel {
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """

        # API配置
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.Password)
        self.api_key_edit.textChanged.connect(self.on_config_changed)
        self.api_key_edit.setStyleSheet(input_style)

        api_key_label = QLabel("API Key:")
        api_key_label.setStyleSheet(label_style)
        ai_form_layout.addRow(api_key_label, self.api_key_edit)

        self.api_url_edit = QLineEdit()
        self.api_url_edit.textChanged.connect(self.on_config_changed)
        self.api_url_edit.setStyleSheet(input_style)

        api_url_label = QLabel("API URL:")
        api_url_label.setStyleSheet(label_style)
        ai_form_layout.addRow(api_url_label, self.api_url_edit)

        self.model_name_edit = QLineEdit()
        self.model_name_edit.textChanged.connect(self.on_config_changed)
        self.model_name_edit.setStyleSheet(input_style)

        model_name_label = QLabel("模型名称:")
        model_name_label.setStyleSheet(label_style)
        ai_form_layout.addRow(model_name_label, self.model_name_edit)

        # 高级设置容器
        advanced_container = QWidget()
        advanced_container.setStyleSheet("""
            QWidget {
                background-color: #1a1a1b;
                border: 1px solid #3a3a3a;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        ai_layout.addWidget(advanced_container)

        advanced_layout = QFormLayout(advanced_container)
        advanced_layout.setContentsMargins(8, 8, 8, 8)
        advanced_layout.setSpacing(8)

        # 统一的SpinBox样式
        spinbox_style = """
            QSpinBox {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 6px;
                padding: 6px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
            }
            QSpinBox:focus {
                border-color: #0078d4;
            }
        """

        self.temperature_spin = QSpinBox()
        self.temperature_spin.setRange(0, 100)
        self.temperature_spin.setValue(70)
        self.temperature_spin.valueChanged.connect(self.on_config_changed)
        self.temperature_spin.setStyleSheet(spinbox_style)

        temp_label = QLabel("Temperature (%):")
        temp_label.setStyleSheet(label_style)
        advanced_layout.addRow(temp_label, self.temperature_spin)

        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(100, 8000)
        self.max_tokens_spin.setValue(4000)
        self.max_tokens_spin.valueChanged.connect(self.on_config_changed)
        self.max_tokens_spin.setStyleSheet(spinbox_style)

        tokens_label = QLabel("Max Tokens:")
        tokens_label.setStyleSheet(label_style)
        advanced_layout.addRow(tokens_label, self.max_tokens_spin)

        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(10, 300)
        self.timeout_spin.setValue(60)
        self.timeout_spin.valueChanged.connect(self.on_config_changed)
        self.timeout_spin.setStyleSheet(spinbox_style)

        timeout_label = QLabel("超时时间(秒):")
        timeout_label.setStyleSheet(label_style)
        advanced_layout.addRow(timeout_label, self.timeout_spin)

        # 添加弹性空间
        ai_layout.addStretch()

    def create_prompt_config_panel(self):
        """创建提示词配置面板"""
        self.prompt_config_panel = QWidget()
        self.prompt_config_panel.setVisible(False)
        self.prompt_config_panel.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
                color: #ffffff;
            }
        """)
        self.config_stack_layout.addWidget(self.prompt_config_panel)

        prompt_layout = QVBoxLayout(self.prompt_config_panel)
        prompt_layout.setContentsMargins(0, 0, 0, 0)
        prompt_layout.setSpacing(12)

        # 提示词编辑区域
        prompt_label = QLabel("AI解析提示词:")
        prompt_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                font-weight: bold;
                margin-bottom: 5px;
            }
        """)
        prompt_layout.addWidget(prompt_label)

        self.prompt_edit = QTextEdit()
        self.prompt_edit.setMinimumHeight(300)
        self.prompt_edit.textChanged.connect(self.on_config_changed)
        self.prompt_edit.setStyleSheet("""
            QTextEdit {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 8px;
                padding: 12px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #0078d4;
            }
        """)
        prompt_layout.addWidget(self.prompt_edit)

        # 提示词操作按钮
        prompt_ops_layout = QHBoxLayout()

        self.reset_prompt_btn = QPushButton("🔄 重置默认")
        self.reset_prompt_btn.clicked.connect(self.reset_default_prompt)
        prompt_ops_layout.addWidget(self.reset_prompt_btn)

        self.test_prompt_btn = QPushButton("🧪 测试提示词")
        self.test_prompt_btn.clicked.connect(self.test_prompt)
        prompt_ops_layout.addWidget(self.test_prompt_btn)

        prompt_ops_layout.addStretch()
        prompt_layout.addLayout(prompt_ops_layout)

    def create_supplier_config_panel(self):
        """创建供应商配置面板"""
        self.supplier_config_panel = QWidget()
        self.supplier_config_panel.setVisible(False)
        self.supplier_config_panel.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
                color: #ffffff;
            }
        """)
        self.config_stack_layout.addWidget(self.supplier_config_panel)

        supplier_layout = QVBoxLayout(self.supplier_config_panel)
        supplier_layout.setContentsMargins(0, 0, 0, 0)
        supplier_layout.setSpacing(12)

        # 供应商列表
        self.supplier_table = QTableWidget()
        self.supplier_table.setColumnCount(3)
        self.supplier_table.setHorizontalHeaderLabels(["供应商代码", "供应商名称", "使用次数"])
        supplier_layout.addWidget(self.supplier_table)

        # 供应商操作按钮
        supplier_ops_layout = QHBoxLayout()

        self.add_supplier_config_btn = QPushButton("➕ 添加供应商")
        self.add_supplier_config_btn.clicked.connect(self.add_supplier_config)
        supplier_ops_layout.addWidget(self.add_supplier_config_btn)

        self.edit_supplier_btn = QPushButton("✏️ 编辑供应商")
        self.edit_supplier_btn.clicked.connect(self.edit_supplier)
        supplier_ops_layout.addWidget(self.edit_supplier_btn)

        self.delete_supplier_btn = QPushButton("🗑️ 删除供应商")
        self.delete_supplier_btn.clicked.connect(self.delete_supplier)
        supplier_ops_layout.addWidget(self.delete_supplier_btn)

        supplier_ops_layout.addStretch()
        supplier_layout.addLayout(supplier_ops_layout)

    def create_ui_config_panel(self):
        """创建界面配置面板"""
        self.ui_config_panel = QWidget()
        self.ui_config_panel.setVisible(False)
        self.ui_config_panel.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
                color: #ffffff;
            }
        """)
        self.config_stack_layout.addWidget(self.ui_config_panel)

        ui_layout = QFormLayout(self.ui_config_panel)
        ui_layout.setContentsMargins(0, 0, 0, 0)
        ui_layout.setSpacing(12)

        # 统一的控件样式
        combo_style = """
            QComboBox {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 6px;
                padding: 6px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
            }
            QComboBox:focus {
                border-color: #0078d4;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """

        checkbox_style = """
            QCheckBox {
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border: 2px solid #0078d4;
                border-radius: 3px;
            }
        """

        # 主题设置
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["深色主题", "浅色主题"])
        self.theme_combo.currentTextChanged.connect(self.on_config_changed)
        self.theme_combo.setStyleSheet(combo_style)

        theme_label = QLabel("界面主题:")
        theme_label.setStyleSheet("color: #ffffff; font-family: 'Microsoft YaHei'; font-size: 12px;")
        ui_layout.addRow(theme_label, self.theme_combo)

        # 字体设置
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 20)
        self.font_size_spin.setValue(10)
        self.font_size_spin.valueChanged.connect(self.on_config_changed)
        self.font_size_spin.setStyleSheet("""
            QSpinBox {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 6px;
                padding: 6px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
            }
            QSpinBox:focus {
                border-color: #0078d4;
            }
        """)

        font_label = QLabel("字体大小:")
        font_label.setStyleSheet("color: #ffffff; font-family: 'Microsoft YaHei'; font-size: 12px;")
        ui_layout.addRow(font_label, self.font_size_spin)

        # 自动保存设置
        self.auto_save_check = QCheckBox("启用自动保存配置")
        self.auto_save_check.setChecked(True)
        self.auto_save_check.toggled.connect(self.on_config_changed)
        self.auto_save_check.setStyleSheet(checkbox_style)
        ui_layout.addRow(self.auto_save_check)

        # 启动设置
        self.remember_window_check = QCheckBox("记住窗口位置和大小")
        self.remember_window_check.setChecked(True)
        self.remember_window_check.toggled.connect(self.on_config_changed)
        self.remember_window_check.setStyleSheet(checkbox_style)
        ui_layout.addRow(self.remember_window_check)

    def create_cookies_config_panel(self):
        """创建Cookies配置面板"""
        self.cookies_config_panel = QWidget()
        self.cookies_config_panel.setVisible(False)
        self.cookies_config_panel.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
                color: #ffffff;
            }
        """)
        self.config_stack_layout.addWidget(self.cookies_config_panel)

        cookies_layout = QVBoxLayout(self.cookies_config_panel)
        cookies_layout.setContentsMargins(0, 0, 0, 0)
        cookies_layout.setSpacing(12)

        # 导入cookies模块
        from modules.cookies import CookiesParser, CookiesValidator, CookiesManager

        # 初始化cookies组件
        self.cookies_parser = CookiesParser()
        self.cookies_validator = CookiesValidator()
        self.cookies_manager = CookiesManager(log_callback=self.main_window.log_message)

        # 状态指示器
        self.create_cookies_status_indicator(cookies_layout)

        # 使用说明
        self.create_cookies_usage_instructions(cookies_layout)

        # Cookies输入区域
        self.create_cookies_input_area(cookies_layout)

        # 操作按钮组
        self.create_cookies_button_group(cookies_layout)

        # 加载当前cookies
        self.load_current_cookies()

    def create_cookies_status_indicator(self, layout):
        """创建cookies状态指示器"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #161617;
                border: 1px solid #3a3a3a;
                border-radius: 12px;
                padding: 10px;
            }
        """)

        status_layout = QHBoxLayout(status_frame)

        # 状态标签
        self.cookies_status_label = QLabel("⚪ 状态: 未知")
        self.cookies_status_label.setStyleSheet("""
            QLabel {
                background-color: #6c757d;
                color: white;
                border-radius: 12px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei';
            }
        """)

        # 最后验证时间标签
        self.cookies_last_check_label = QLabel("最后检查: 从未")
        self.cookies_last_check_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 11px;
                font-family: 'Microsoft YaHei';
                margin-left: 10px;
            }
        """)

        status_layout.addWidget(self.cookies_status_label)
        status_layout.addWidget(self.cookies_last_check_label)
        status_layout.addStretch()

        layout.addWidget(status_frame)

    def create_cookies_usage_instructions(self, layout):
        """创建cookies使用说明"""
        instructions = QLabel("""
📋 <b>使用说明</b>：
• 支持多种格式：浏览器开发者工具格式、JSON格式、键值对格式
• 点击"🔗 直达ERP系统"按钮打开ERP页面，从浏览器开发者工具复制cookies
• 粘贴到下方文本框，点击"🔍 解析"按钮自动识别格式
• 点击"✅ 验证"测试cookies有效性，"💾 保存"保存到系统
        """)
        instructions.setStyleSheet("""
            QLabel {
                color: #cccccc;
                font-size: 11px;
                font-family: 'Microsoft YaHei';
                background-color: #1a1a1a;
                border: 1px solid #333333;
                border-radius: 8px;
                padding: 12px;
                line-height: 1.4;
            }
        """)
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

    def create_cookies_input_area(self, layout):
        """创建cookies输入区域"""
        input_label = QLabel("📝 Cookies 输入区域:")
        input_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-weight: bold;
                font-size: 13px;
                font-family: 'Microsoft YaHei';
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(input_label)

        # 创建输入文本框
        self.cookies_input = QTextEdit()
        self.cookies_input.setPlaceholderText("""请粘贴cookies，支持以下格式：

1. 浏览器格式：
_sid18707109=ABC123; u_id=18707109; u_co_id=13881863; isLogin=true

2. JSON格式：
{"_sid18707109": "ABC123", "u_id": "18707109", "u_co_id": "13881863"}

3. 键值对格式：
_sid18707109: ABC123
u_id: 18707109
u_co_id: 13881863""")

        self.cookies_input.setStyleSheet("""
            QTextEdit {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 8px;
                padding: 12px;
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #0078d4;
            }
        """)
        self.cookies_input.setMinimumHeight(200)
        self.cookies_input.setAcceptDrops(True)

        # 添加纯文本粘贴功能
        self.cookies_input.insertFromMimeData = self._insert_plain_text

        layout.addWidget(self.cookies_input)

    def create_cookies_button_group(self, layout):
        """创建cookies操作按钮组"""
        button_layout = QHBoxLayout()

        # 解析按钮
        self.parse_cookies_btn = QPushButton("🔍 解析")
        self.parse_cookies_btn.setToolTip("自动识别并解析cookies格式")
        self.parse_cookies_btn.clicked.connect(self.parse_cookies)

        # 验证按钮
        self.validate_cookies_btn = QPushButton("✅ 验证")
        self.validate_cookies_btn.setToolTip("测试cookies是否有效")
        self.validate_cookies_btn.clicked.connect(self.validate_cookies)

        # 保存按钮
        self.save_cookies_btn = QPushButton("💾 保存")
        self.save_cookies_btn.setToolTip("保存cookies到系统（完全替换现有cookies）")
        self.save_cookies_btn.clicked.connect(self.save_cookies)

        # 清空按钮
        self.clear_cookies_btn = QPushButton("🗑 清空")
        self.clear_cookies_btn.setToolTip("清空输入框和重置状态")
        self.clear_cookies_btn.clicked.connect(self.clear_cookies)

        # 直达ERP按钮
        self.open_erp_btn = QPushButton("🔗 直达ERP系统")
        self.open_erp_btn.setToolTip("打开ERP系统页面")
        self.open_erp_btn.clicked.connect(self.open_erp_system)

        # 设置按钮样式
        button_style = """
            QPushButton {
                background-color: #0071e3;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei';
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #005bb5;
            }
            QPushButton:pressed {
                background-color: #004080;
            }
            QPushButton:disabled {
                background-color: #555555;
                color: #999999;
            }
        """

        # 特殊按钮样式
        clear_style = button_style.replace("#0071e3", "#dc3545").replace("#005bb5", "#c82333").replace("#004080", "#bd2130")
        open_erp_style = button_style.replace("#0071e3", "#02e87d").replace("#005bb5", "#02d46f").replace("#004080", "#02c061")

        for btn in [self.parse_cookies_btn, self.validate_cookies_btn, self.save_cookies_btn]:
            btn.setStyleSheet(button_style)

        self.clear_cookies_btn.setStyleSheet(clear_style)
        self.open_erp_btn.setStyleSheet(open_erp_style)

        # 添加按钮到布局
        button_layout.addWidget(self.parse_cookies_btn)
        button_layout.addWidget(self.validate_cookies_btn)
        button_layout.addWidget(self.save_cookies_btn)
        button_layout.addWidget(self.clear_cookies_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.open_erp_btn)

        layout.addLayout(button_layout)

        # 解析结果显示区域 - 简洁无边框设计
        result_container = QWidget()
        result_container.setStyleSheet("""
            QWidget {
                background-color: transparent;
                margin: 0px;
                padding: 0px;
            }
        """)
        layout.addWidget(result_container)

        # 使用无边距的垂直布局
        result_layout = QVBoxLayout(result_container)
        result_layout.setContentsMargins(0, 8, 0, 0)  # 只保留顶部小间距
        result_layout.setSpacing(0)

        # 解析结果表格 - 优化样式，完全占满容器
        self.cookies_result_table = QTableWidget()
        self.cookies_result_table.setColumnCount(2)
        self.cookies_result_table.setHorizontalHeaderLabels(["Cookie名称", "Cookie值"])
        self.cookies_result_table.horizontalHeader().setStretchLastSection(True)
        self.cookies_result_table.setAlternatingRowColors(True)
        self.cookies_result_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.cookies_result_table.setMaximumHeight(400)
        self.cookies_result_table.setStyleSheet("""
            QTableWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #444444;
                border-radius: 6px;
                font-family: 'Microsoft YaHei';
                font-size: 10px;
                gridline-color: #444444;
            }
            QTableWidget::item {
                padding: 6px 8px;
                border-bottom: 1px solid #3a3a3a;
                border-right: 1px solid #3a3a3a;
            }
            QTableWidget::item:selected {
                background-color: #0071e3;
                color: #ffffff;
            }
            QTableWidget::item:hover {
                background-color: #404040;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #0071e3;
                font-weight: bold;
                font-size: 11px;
            }
            QHeaderView::section:hover {
                background-color: #4a4a4a;
            }
            QScrollBar:vertical {
                background-color: #2b2b2b;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #666666;
            }
        """)
        result_layout.addWidget(self.cookies_result_table)

    def _insert_plain_text(self, source):
        """插入纯文本，去除富文本格式"""
        if source.hasText():
            # 获取纯文本并清理格式
            text = source.text()
            # 去除可能的富文本标记
            import re
            text = re.sub(r'<[^>]+>', '', text)
            # 插入纯文本
            cursor = self.cookies_input.textCursor()
            cursor.insertText(text)

    def load_current_cookies(self):
        """加载当前cookies"""
        try:
            cookies = self.cookies_manager.load_cookies()
            if cookies:
                self.update_cookies_status("unknown", f"已加载 {len(cookies)} 个cookies，需要验证")
            else:
                self.update_cookies_status("unknown", "未找到cookies文件")
        except Exception as e:
            self.update_cookies_status("invalid", f"加载cookies失败: {str(e)}")

    def update_cookies_status(self, status: str, message: str = ""):
        """更新cookies状态显示"""
        if status == "valid":
            self.cookies_status_label.setText("🟢 状态: 有效")
            self.cookies_status_label.setStyleSheet("""
                QLabel {
                    background-color: #28a745;
                    color: white;
                    border-radius: 12px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei';
                }
            """)
            if hasattr(self.cookies_manager, 'last_validation_time') and self.cookies_manager.last_validation_time:
                time_str = self.cookies_manager.last_validation_time.strftime("%H:%M:%S")
                self.cookies_last_check_label.setText(f"最后检查: {time_str}")
        elif status == "invalid":
            self.cookies_status_label.setText("🔴 状态: 无效")
            self.cookies_status_label.setStyleSheet("""
                QLabel {
                    background-color: #dc3545;
                    color: white;
                    border-radius: 12px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei';
                }
            """)
            self.cookies_last_check_label.setText(f"错误: {message}")
        else:  # unknown
            self.cookies_status_label.setText("⚪ 状态: 未知")
            self.cookies_status_label.setStyleSheet("""
                QLabel {
                    background-color: #6c757d;
                    color: white;
                    border-radius: 12px;
                    padding: 8px 16px;
                    font-weight: bold;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei';
                }
            """)
            self.cookies_last_check_label.setText("需要验证")

        # 更新管理器状态
        self.cookies_manager.update_validation_status(status, message)

    def parse_cookies(self):
        """解析cookies（使用简化方法）"""
        try:
            text = self.cookies_input.toPlainText().strip()
            if not text:
                self.main_window.log_message("请先输入cookies内容", "WARNING")
                return

            # 使用主窗口的简化解析方法
            parsed_cookies = self.main_window.parse_cookie_text(text)

            if parsed_cookies:
                # 更新管理器中的cookies
                self.cookies_manager.update_cookies(parsed_cookies)
                self.main_window.log_message(f"成功解析 {len(parsed_cookies)} 个cookies", "DEBUG")

                # 显示解析结果到表格
                self.display_parsed_cookies(parsed_cookies)

                # 显示简要提示
                QMessageBox.information(
                    self.main_window,
                    "解析成功",
                    f"✅ 成功解析 {len(parsed_cookies)} 个cookies\n\n"
                    f"解析结果已显示在下方表格中，请检查后进行验证和保存。"
                )
                self.update_cookies_status("unknown", "已解析，需要验证")
            else:
                self.main_window.log_message("cookies解析失败，请检查格式", "ERROR")
                # 清空表格
                self.cookies_result_table.setRowCount(0)

        except Exception as e:
            self.main_window.log_message(f"解析cookies异常: {str(e)}", "ERROR")
            QMessageBox.critical(self.main_window, "解析错误", f"解析过程中发生错误:\n{str(e)}")
            # 清空表格
            self.cookies_result_table.setRowCount(0)

    def display_parsed_cookies(self, cookies):
        """显示解析后的cookies到表格"""
        try:
            # 清空现有内容
            self.cookies_result_table.setRowCount(0)

            # 添加cookies到表格
            for i, (key, value) in enumerate(cookies.items()):
                self.cookies_result_table.insertRow(i)

                # Cookie名称
                name_item = QTableWidgetItem(key)
                name_item.setToolTip(key)
                self.cookies_result_table.setItem(i, 0, name_item)

                # Cookie值（截断显示，完整值在tooltip中）
                display_value = value[:50] + "..." if len(value) > 50 else value
                value_item = QTableWidgetItem(display_value)
                value_item.setToolTip(f"完整值: {value}")
                self.cookies_result_table.setItem(i, 1, value_item)

            # 调整列宽
            self.cookies_result_table.resizeColumnsToContents()

        except Exception as e:
            self.main_window.log_message(f"显示解析结果异常: {str(e)}", "ERROR")

    def validate_cookies(self):
        """验证cookies有效性（使用简化方法）"""
        current_cookies = self.cookies_manager.get_current_cookies()
        if not current_cookies:
            self.main_window.log_message("请先解析cookies", "WARNING")
            QMessageBox.warning(self.main_window, "验证失败", "请先输入并解析cookies")
            return

        try:
            # 禁用验证按钮，显示进度
            self.validate_cookies_btn.setEnabled(False)
            self.validate_cookies_btn.setText("🔄 验证中...")

            # 先更新ERP集成器的cookies
            if hasattr(self.main_window, 'erp_integration'):
                self.main_window.erp_integration.update_cookies(current_cookies)

            # 使用主窗口的简化验证方法
            is_valid = self.main_window.check_erp_auth_status()

            if is_valid:
                self.update_cookies_status("valid", "验证成功")
                self.main_window.log_message("✅ Cookies验证成功", "DEBUG")
                QMessageBox.information(self.main_window, "验证成功", "Cookies有效，可以正常使用ERP功能")
            else:
                self.update_cookies_status("invalid", "ERP认证失败")
                self.main_window.log_message(f"❌ Cookies验证失败", "ERROR")
                QMessageBox.warning(self.main_window, "验证失败", f"Cookies验证失败，请检查cookies是否正确或已过期")

        except Exception as e:
            self.update_cookies_status("invalid", f"验证异常: {str(e)}")
            self.main_window.log_message(f"验证cookies异常: {str(e)}", "ERROR")
            QMessageBox.critical(self.main_window, "验证错误", f"验证过程中发生错误:\n{str(e)}")

        finally:
            # 恢复验证按钮
            self.validate_cookies_btn.setEnabled(True)
            self.validate_cookies_btn.setText("✅ 验证")

    def save_cookies(self):
        """保存cookies到文件"""
        current_cookies = self.cookies_manager.get_current_cookies()
        if not current_cookies:
            self.main_window.log_message("没有可保存的cookies", "WARNING")
            QMessageBox.warning(self.main_window, "保存失败", "请先输入并解析cookies")
            return

        try:
            # 询问用户是否确认保存
            reply = QMessageBox.question(
                self.main_window,
                "确认保存",
                f"确定要保存 {len(current_cookies)} 个cookies到系统吗？\n\n这将覆盖现有的cookies配置。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 保存到文件
                self.cookies_manager.save_cookies(current_cookies)

                # 更新主程序的ERP集成器
                if hasattr(self.main_window, 'erp_integration'):
                    self.main_window.erp_integration.cookies.update(current_cookies)
                    self.main_window.erp_integration.auth_valid = False  # 重置认证状态
                    self.main_window.log_message("已更新ERP集成器的cookies", "DEBUG")

                QMessageBox.information(self.main_window, "保存成功", f"已成功保存 {len(current_cookies)} 个cookies")

        except Exception as e:
            self.main_window.log_message(f"保存cookies失败: {str(e)}", "ERROR")
            QMessageBox.critical(self.main_window, "保存错误", f"保存过程中发生错误:\n{str(e)}")

    def clear_cookies(self):
        """清空cookies和重置状态"""
        reply = QMessageBox.question(
            self.main_window,
            "确认清空",
            "确定要清空所有cookies输入和重置状态吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.cookies_input.clear()
            self.cookies_manager.clear_cookies()
            self.cookies_result_table.setRowCount(0)  # 清空解析结果表格
            self.update_cookies_status("unknown", "已清空")
            self.main_window.log_message("已清空cookies输入", "DEBUG")

    def open_erp_system(self):
        """打开ERP系统页面"""
        try:
            import webbrowser
            erp_url = "https://src.erp321.com/erp-web-group/erp-scm-goods/goodsInventoryManagement?tabAllow=camera&_c=jst-epaas"
            webbrowser.open(erp_url)
            self.main_window.log_message("已打开ERP系统页面")

            # 显示操作提示
            QMessageBox.information(
                self.main_window,
                "ERP系统已打开",
                "ERP系统页面已在浏览器中打开。\n\n"
                "请按以下步骤获取cookies：\n"
                "1. 在ERP页面登录您的账户\n"
                "2. 按F12打开开发者工具\n"
                "3. 切换到Network(网络)标签\n"
                "4. 刷新页面或进行任意操作\n"
                "5. 找到任意请求，复制Request Headers中的Cookie值\n"
                "6. 粘贴到下方输入框中"
            )

        except Exception as e:
            self.main_window.log_message(f"打开ERP系统失败: {str(e)}", "ERROR")
            QMessageBox.critical(self.main_window, "打开失败", f"无法打开ERP系统:\n{str(e)}")

    def show_config_panel(self, panel_name):
        """显示指定的配置面板"""
        # 隐藏所有面板
        self.ai_config_panel.setVisible(False)
        self.prompt_config_panel.setVisible(False)
        self.supplier_config_panel.setVisible(False)
        self.ui_config_panel.setVisible(False)
        self.cookies_config_panel.setVisible(False)
        self.smart_memory_config_panel.setVisible(False)

        # 显示指定面板
        if panel_name == "ai":
            self.ai_config_panel.setVisible(True)
        elif panel_name == "prompt":
            self.prompt_config_panel.setVisible(True)
        elif panel_name == "supplier":
            self.supplier_config_panel.setVisible(True)
        elif panel_name == "ui":
            self.ui_config_panel.setVisible(True)
        elif panel_name == "cookies":
            self.cookies_config_panel.setVisible(True)
        elif panel_name == "smart_memory":
            self.smart_memory_config_panel.setVisible(True)

    def on_config_category_changed(self, row):
        """配置分类变更事件"""
        categories = ["ai", "prompt", "supplier", "ui", "cookies", "smart_memory"]
        if 0 <= row < len(categories):
            self.show_config_panel(categories[row])

    def load_configuration(self):
        """加载配置"""
        try:
            config = self.main_window.config_manager.get_config()

            # 加载AI配置
            ai_config = config.get("ai", {})
            self.api_key_edit.setText(ai_config.get("api_key", ""))
            self.api_url_edit.setText(ai_config.get("api_url", ""))
            self.model_name_edit.setText(ai_config.get("model_name", ""))
            
            if hasattr(self, 'temperature_spin'):
                self.temperature_spin.setValue(int(ai_config.get("temperature", 70)))
            if hasattr(self, 'max_tokens_spin'):
                self.max_tokens_spin.setValue(int(ai_config.get("max_tokens", 4000)))
            if hasattr(self, 'timeout_spin'):
                self.timeout_spin.setValue(int(ai_config.get("timeout", 60)))

            # 加载提示词
            prompt = self.main_window.config_manager.get_prompt()
            # 确保prompt是字符串类型
            if isinstance(prompt, dict):
                prompt = str(prompt)
            elif prompt is None:
                prompt = ""
            self.prompt_edit.setPlainText(prompt)

            # 加载界面设置
            ui_config = config.get("ui", {})
            if hasattr(self, 'theme_combo'):
                theme_index = 0 if ui_config.get("theme", "dark") == "dark" else 1
                self.theme_combo.setCurrentIndex(theme_index)
            if hasattr(self, 'font_size_spin'):
                self.font_size_spin.setValue(int(ui_config.get("font_size", 10)))
            if hasattr(self, 'auto_save_check'):
                self.auto_save_check.setChecked(ui_config.get("auto_save", True))
            if hasattr(self, 'remember_window_check'):
                self.remember_window_check.setChecked(ui_config.get("remember_window", True))

            # 加载供应商数据
            self.load_suppliers()

            # 加载智能记忆设置
            self.load_smart_memory_settings()

            self.main_window.log_message("配置加载完成", "DEBUG")
            
        except Exception as e:
            self.main_window.log_message(f"加载配置失败: {str(e)}", "ERROR")

    def save_configuration(self):
        """保存配置"""
        try:
            config = {
                "ai": {
                    "api_key": self.api_key_edit.text(),
                    "api_url": self.api_url_edit.text(),
                    "model_name": self.model_name_edit.text()
                },
                "ui": {}
            }

            # 保存AI高级设置
            if hasattr(self, 'temperature_spin'):
                config["ai"]["temperature"] = self.temperature_spin.value()
            if hasattr(self, 'max_tokens_spin'):
                config["ai"]["max_tokens"] = self.max_tokens_spin.value()
            if hasattr(self, 'timeout_spin'):
                config["ai"]["timeout"] = self.timeout_spin.value()

            # 保存界面设置
            if hasattr(self, 'theme_combo'):
                config["ui"]["theme"] = "dark" if self.theme_combo.currentIndex() == 0 else "light"
            if hasattr(self, 'font_size_spin'):
                config["ui"]["font_size"] = self.font_size_spin.value()
            if hasattr(self, 'auto_save_check'):
                config["ui"]["auto_save"] = self.auto_save_check.isChecked()
            if hasattr(self, 'remember_window_check'):
                config["ui"]["remember_window"] = self.remember_window_check.isChecked()

            # 保存提示词（直接保存为字符串）
            config["prompt"] = self.prompt_edit.toPlainText()

            # 保存到配置管理器
            self.main_window.config_manager.save_config(config)
            
            self.main_window.log_message("配置已保存", "DEBUG")
            
        except Exception as e:
            self.main_window.log_message(f"保存配置失败: {str(e)}", "ERROR")

    def create_smart_memory_config_panel(self):
        """创建智能记忆配置面板"""
        self.smart_memory_config_panel = QWidget()
        self.smart_memory_config_panel.setVisible(False)
        self.smart_memory_config_panel.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
                color: #ffffff;
            }
        """)
        self.config_stack_layout.addWidget(self.smart_memory_config_panel)

        layout = QVBoxLayout(self.smart_memory_config_panel)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)

        # 标题
        title_label = QLabel("🧠 智能记忆管理")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title_label.setStyleSheet("color: #ffffff; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 统计信息区域
        self.create_memory_stats_section(layout)

        # 设置区域
        self.create_memory_settings_section(layout)

        # 记忆列表区域
        self.create_memory_list_section(layout)

        # 操作按钮区域
        self.create_memory_actions_section(layout)

    def create_memory_stats_section(self, parent_layout):
        """创建记忆统计信息区域"""
        stats_group = QGroupBox("📊 统计信息")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3a3a3a;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        parent_layout.addWidget(stats_group)

        stats_layout = QHBoxLayout(stats_group)

        # 统计标签
        self.total_memories_label = QLabel("总记忆数: 0")
        self.total_usage_label = QLabel("总使用次数: 0")
        self.temp_memories_label = QLabel("临时记忆: 0")

        for label in [self.total_memories_label, self.total_usage_label, self.temp_memories_label]:
            label.setStyleSheet("color: #ffffff; font-size: 12px; padding: 5px;")
            stats_layout.addWidget(label)

        stats_layout.addStretch()

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新")
        refresh_btn.clicked.connect(self.refresh_memory_stats)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #0071e3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #005bb5;
            }
        """)
        stats_layout.addWidget(refresh_btn)

    def create_memory_settings_section(self, parent_layout):
        """创建记忆设置区域"""
        settings_group = QGroupBox("⚙️ 设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3a3a3a;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        parent_layout.addWidget(settings_group)

        settings_layout = QFormLayout(settings_group)

        # 启用智能记忆
        self.memory_enabled_check = QCheckBox("启用智能记忆功能")
        self.memory_enabled_check.setChecked(True)
        self.memory_enabled_check.toggled.connect(self.on_memory_settings_changed)
        self.memory_enabled_check.setStyleSheet("""
            QCheckBox {
                color: #ffffff;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border: 2px solid #0078d4;
                border-radius: 3px;
            }
        """)
        settings_layout.addRow(self.memory_enabled_check)

        # 上传时自动保存
        self.auto_save_on_upload_check = QCheckBox("上传时自动保存记忆")
        self.auto_save_on_upload_check.setChecked(True)
        self.auto_save_on_upload_check.toggled.connect(self.on_memory_settings_changed)
        self.auto_save_on_upload_check.setStyleSheet(self.memory_enabled_check.styleSheet())
        settings_layout.addRow(self.auto_save_on_upload_check)

        # 自动确认阈值
        threshold_label = QLabel("自动确认置信度阈值:")
        threshold_label.setStyleSheet("color: #ffffff; font-size: 12px;")

        self.auto_confirm_threshold_spin = QDoubleSpinBox()
        self.auto_confirm_threshold_spin.setRange(0.0, 1.0)
        self.auto_confirm_threshold_spin.setSingleStep(0.1)
        self.auto_confirm_threshold_spin.setValue(0.8)
        self.auto_confirm_threshold_spin.valueChanged.connect(self.on_memory_settings_changed)
        self.auto_confirm_threshold_spin.setStyleSheet("""
            QDoubleSpinBox {
                background-color: #161617;
                border: 2px solid #3a3a3a;
                border-radius: 6px;
                padding: 6px;
                color: #ffffff;
                font-size: 12px;
            }
            QDoubleSpinBox:focus {
                border-color: #0078d4;
            }
        """)
        settings_layout.addRow(threshold_label, self.auto_confirm_threshold_spin)

    def create_memory_list_section(self, parent_layout):
        """创建记忆列表区域"""
        list_group = QGroupBox("📋 记忆列表")
        list_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3a3a3a;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        parent_layout.addWidget(list_group)

        list_layout = QVBoxLayout(list_group)

        # 记忆表格
        self.memory_table = QTableWidget()
        self.memory_table.setColumnCount(6)
        self.memory_table.setHorizontalHeaderLabels([
            "款号", "颜色规格", "确认颜色", "使用次数", "最后使用", "置信度"
        ])

        # 设置表格样式
        self.memory_table.setStyleSheet("""
            QTableWidget {
                background-color: #161617;
                border: 1px solid #3a3a3a;
                border-radius: 6px;
                color: #ffffff;
                gridline-color: #3a3a3a;
                selection-background-color: #0078d4;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #3a3a3a;
            }
            QHeaderView::section {
                background-color: #2d2d2d;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #3a3a3a;
                font-weight: bold;
            }
        """)

        # 设置表格属性
        self.memory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.memory_table.setAlternatingRowColors(True)
        self.memory_table.horizontalHeader().setStretchLastSection(True)
        self.memory_table.setMaximumHeight(300)

        list_layout.addWidget(self.memory_table)

    def create_memory_actions_section(self, parent_layout):
        """创建记忆操作按钮区域"""
        actions_layout = QHBoxLayout()

        # 删除选中记忆
        delete_btn = QPushButton("🗑️ 删除选中")
        delete_btn.clicked.connect(self.delete_selected_memory)

        # 导出记忆
        export_btn = QPushButton("📤 导出记忆")
        export_btn.clicked.connect(self.export_memories)

        # 导入记忆
        import_btn = QPushButton("📥 导入记忆")
        import_btn.clicked.connect(self.import_memories)

        # 清空所有记忆
        clear_btn = QPushButton("🧹 清空所有")
        clear_btn.clicked.connect(self.clear_all_memories)

        # 按钮样式
        button_style = """
            QPushButton {
                background-color: #0071e3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #005bb5;
            }
            QPushButton:pressed {
                background-color: #004080;
            }
        """

        # 危险操作按钮样式
        danger_button_style = """
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """

        delete_btn.setStyleSheet(danger_button_style)
        clear_btn.setStyleSheet(danger_button_style)
        export_btn.setStyleSheet(button_style)
        import_btn.setStyleSheet(button_style)

        actions_layout.addWidget(delete_btn)
        actions_layout.addWidget(export_btn)
        actions_layout.addWidget(import_btn)
        actions_layout.addStretch()
        actions_layout.addWidget(clear_btn)

        parent_layout.addLayout(actions_layout)

    def refresh_memory_stats(self):
        """刷新记忆统计信息"""
        try:
            if not hasattr(self.main_window, 'smart_memory_manager'):
                return

            memory_manager = self.main_window.smart_memory_manager
            stats = memory_manager.get_memory_statistics()

            self.total_memories_label.setText(f"总记忆数: {stats.get('total_memories', 0)}")
            self.total_usage_label.setText(f"总使用次数: {stats.get('total_usage', 0)}")
            self.temp_memories_label.setText(f"临时记忆: {stats.get('temp_memories', 0)}")

            # 刷新记忆列表
            self.refresh_memory_list()

        except Exception as e:
            self.main_window.log_message(f"刷新记忆统计失败: {str(e)}", "ERROR")

    def refresh_memory_list(self):
        """刷新记忆列表"""
        try:
            if not hasattr(self.main_window, 'smart_memory_manager'):
                return

            memory_manager = self.main_window.smart_memory_manager

            # 清空表格
            self.memory_table.setRowCount(0)

            # 添加记忆数据
            for memory_key, memory in memory_manager.memories.items():
                row = self.memory_table.rowCount()
                self.memory_table.insertRow(row)

                # 款号
                self.memory_table.setItem(row, 0, QTableWidgetItem(memory.sku_code))

                # 颜色规格
                self.memory_table.setItem(row, 1, QTableWidgetItem(memory.color_spec))

                # 确认颜色（显示款号-颜色格式，便于区分不同款号）
                if len(memory.confirmed_colors) == 1:
                    # 单个颜色：显示为 "款号-颜色"
                    colors_text = f"{memory.sku_code}-{memory.confirmed_colors[0]}"
                else:
                    # 多个颜色：显示为 "款号-(颜色1,颜色2)"
                    colors_list = ",".join(memory.confirmed_colors)
                    colors_text = f"{memory.sku_code}-({colors_list})"
                self.memory_table.setItem(row, 2, QTableWidgetItem(colors_text))

                # 使用次数
                self.memory_table.setItem(row, 3, QTableWidgetItem(str(memory.usage_count)))

                # 最后使用
                last_used = memory.last_used[:19] if len(memory.last_used) > 19 else memory.last_used
                self.memory_table.setItem(row, 4, QTableWidgetItem(last_used))

                # 置信度
                confidence_text = f"{memory.confidence:.2f}"
                self.memory_table.setItem(row, 5, QTableWidgetItem(confidence_text))

                # 存储记忆键值用于删除操作
                self.memory_table.item(row, 0).setData(Qt.UserRole, memory_key)

            # 调整列宽
            self.memory_table.resizeColumnsToContents()

        except Exception as e:
            self.main_window.log_message(f"刷新记忆列表失败: {str(e)}", "ERROR")

    def on_memory_settings_changed(self):
        """记忆设置变更事件"""
        try:
            if not hasattr(self.main_window, 'smart_memory_manager'):
                return

            memory_manager = self.main_window.smart_memory_manager

            # 更新设置
            new_settings = {
                "enabled": self.memory_enabled_check.isChecked(),
                "auto_save_on_upload": self.auto_save_on_upload_check.isChecked(),
                "auto_confirm_threshold": self.auto_confirm_threshold_spin.value()
            }

            memory_manager.update_settings(new_settings)
            self.main_window.log_message("智能记忆设置已更新", "DEBUG")

        except Exception as e:
            self.main_window.log_message(f"更新记忆设置失败: {str(e)}", "ERROR")

    def delete_selected_memory(self):
        """删除选中的记忆"""
        try:
            current_row = self.memory_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self.main_window, "警告", "请先选择要删除的记忆记录")
                return

            # 确认删除
            reply = QMessageBox.question(
                self.main_window,
                "确认删除",
                "确定要删除选中的记忆记录吗？此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 获取记忆键值
            memory_key = self.memory_table.item(current_row, 0).data(Qt.UserRole)

            if not hasattr(self.main_window, 'smart_memory_manager'):
                return

            memory_manager = self.main_window.smart_memory_manager
            success = memory_manager.delete_memory(memory_key)

            if success:
                self.main_window.log_message(f"已删除记忆: {memory_key}")
                self.refresh_memory_stats()
            else:
                QMessageBox.warning(self.main_window, "错误", "删除记忆失败")

        except Exception as e:
            self.main_window.log_message(f"删除记忆失败: {str(e)}", "ERROR")
            QMessageBox.critical(self.main_window, "错误", f"删除记忆失败: {str(e)}")

    def export_memories(self):
        """导出记忆数据"""
        try:
            if not hasattr(self.main_window, 'smart_memory_manager'):
                return

            # 选择导出文件
            file_path, _ = QFileDialog.getSaveFileName(
                self.main_window,
                "导出智能记忆",
                "smart_memory_export.json",
                "JSON文件 (*.json)"
            )

            if not file_path:
                return

            memory_manager = self.main_window.smart_memory_manager
            success = memory_manager.export_memories(file_path)

            if success:
                QMessageBox.information(self.main_window, "成功", f"记忆数据已导出到: {file_path}")
                self.main_window.log_message(f"记忆数据已导出: {file_path}")
            else:
                QMessageBox.warning(self.main_window, "错误", "导出记忆数据失败")

        except Exception as e:
            self.main_window.log_message(f"导出记忆失败: {str(e)}", "ERROR")
            QMessageBox.critical(self.main_window, "错误", f"导出记忆失败: {str(e)}")

    def import_memories(self):
        """导入记忆数据"""
        try:
            if not hasattr(self.main_window, 'smart_memory_manager'):
                return

            # 选择导入文件
            file_path, _ = QFileDialog.getOpenFileName(
                self.main_window,
                "导入智能记忆",
                "",
                "JSON文件 (*.json)"
            )

            if not file_path:
                return

            # 询问是否合并
            reply = QMessageBox.question(
                self.main_window,
                "导入模式",
                "选择导入模式：\n\n是(Yes) - 合并到现有记忆\n否(No) - 替换所有记忆",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Cancel:
                return

            merge = (reply == QMessageBox.Yes)

            memory_manager = self.main_window.smart_memory_manager
            success = memory_manager.import_memories(file_path, merge)

            if success:
                QMessageBox.information(self.main_window, "成功", f"记忆数据已导入: {file_path}")
                self.main_window.log_message(f"记忆数据已导入: {file_path}")
                self.refresh_memory_stats()
            else:
                QMessageBox.warning(self.main_window, "错误", "导入记忆数据失败")

        except Exception as e:
            self.main_window.log_message(f"导入记忆失败: {str(e)}", "ERROR")
            QMessageBox.critical(self.main_window, "错误", f"导入记忆失败: {str(e)}")

    def clear_all_memories(self):
        """清空所有记忆"""
        try:
            if not hasattr(self.main_window, 'smart_memory_manager'):
                return

            # 确认清空
            reply = QMessageBox.question(
                self.main_window,
                "确认清空",
                "确定要清空所有记忆数据吗？\n\n此操作将删除所有已保存的智能记忆，不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            memory_manager = self.main_window.smart_memory_manager
            success = memory_manager.clear_all_memories()

            if success:
                QMessageBox.information(self.main_window, "成功", "所有记忆数据已清空")
                self.main_window.log_message("所有记忆数据已清空")
                self.refresh_memory_stats()
            else:
                QMessageBox.warning(self.main_window, "错误", "清空记忆数据失败")

        except Exception as e:
            self.main_window.log_message(f"清空记忆失败: {str(e)}", "ERROR")
            QMessageBox.critical(self.main_window, "错误", f"清空记忆失败: {str(e)}")

    def load_smart_memory_settings(self):
        """加载智能记忆设置"""
        try:
            if not hasattr(self.main_window, 'smart_memory_manager'):
                return

            memory_manager = self.main_window.smart_memory_manager
            settings = memory_manager.settings

            # 更新界面控件
            if hasattr(self, 'memory_enabled_check'):
                self.memory_enabled_check.setChecked(settings.get("enabled", True))
            if hasattr(self, 'auto_save_on_upload_check'):
                self.auto_save_on_upload_check.setChecked(settings.get("auto_save_on_upload", True))
            if hasattr(self, 'auto_confirm_threshold_spin'):
                self.auto_confirm_threshold_spin.setValue(settings.get("auto_confirm_threshold", 0.8))

            # 刷新统计信息
            self.refresh_memory_stats()

        except Exception as e:
            self.main_window.log_message(f"加载智能记忆设置失败: {str(e)}", "ERROR")

    def reset_configuration(self):
        """重置配置"""
        reply = QMessageBox.question(
            self.main_window, 
            "确认重置", 
            "确定要重置所有配置吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 重置为默认配置
                default_config = self.main_window.config_manager.get_default_config()
                self.main_window.config_manager.save_config(default_config)
                
                # 重新加载
                self.load_configuration()
                
                self.main_window.log_message("配置已重置")
                
            except Exception as e:
                self.main_window.log_message(f"重置配置失败: {str(e)}", "ERROR")

    def on_config_changed(self):
        """配置改变时的回调"""
        # 如果启用了自动保存，则自动保存配置
        if hasattr(self, 'auto_save_check') and self.auto_save_check.isChecked():
            # 延迟保存，避免频繁保存
            if hasattr(self, 'save_timer'):
                self.save_timer.stop()
            else:
                from PyQt5.QtCore import QTimer
                self.save_timer = QTimer()
                self.save_timer.setSingleShot(True)
                self.save_timer.timeout.connect(self.save_configuration)

            self.save_timer.start(1000)  # 1秒后保存

    def reset_default_prompt(self):
        """重置默认提示词"""
        try:
            default_prompt = self.main_window.config_manager.get_default_prompt()
            self.prompt_edit.setPlainText(default_prompt)
            self.main_window.log_message("提示词已重置为默认")
        except Exception as e:
            self.main_window.log_message(f"重置提示词失败: {str(e)}", "ERROR")

    def test_prompt(self):
        """测试提示词"""
        # 这里可以实现提示词测试功能
        self.main_window.log_message("提示词测试功能待实现")

    def add_supplier_config(self):
        """添加供应商配置"""
        # 打开添加供应商对话框
        self.main_window.log_message("添加供应商功能待实现")

    def edit_supplier(self):
        """编辑供应商"""
        current_row = self.supplier_table.currentRow()
        if current_row >= 0:
            self.main_window.log_message("编辑供应商功能待实现")
        else:
            self.main_window.log_message("请先选择要编辑的供应商", "WARNING")

    def delete_supplier(self):
        """删除供应商"""
        current_row = self.supplier_table.currentRow()
        if current_row >= 0:
            self.main_window.log_message("删除供应商功能待实现")
        else:
            self.main_window.log_message("请先选择要删除的供应商", "WARNING")

    def load_suppliers(self):
        """加载供应商数据"""
        try:
            # 从供应商管理器加载数据
            if hasattr(self.main_window, 'supplier_manager'):
                suppliers = self.main_window.supplier_manager.get_all_suppliers()
                
                # 清空表格
                self.supplier_table.setRowCount(0)
                
                # 添加供应商数据
                for i, supplier in enumerate(suppliers):
                    self.supplier_table.insertRow(i)
                    self.supplier_table.setItem(i, 0, QTableWidgetItem(supplier.code))
                    self.supplier_table.setItem(i, 1, QTableWidgetItem(supplier.name))
                    self.supplier_table.setItem(i, 2, QTableWidgetItem(str(supplier.usage_count)))
                    
        except Exception as e:
            self.main_window.log_message(f"加载供应商数据失败: {str(e)}", "ERROR")