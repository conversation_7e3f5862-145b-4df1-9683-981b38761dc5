#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SKU智能匹配器模块
实现复杂的SKU匹配逻辑，支持多种匹配策略
"""

from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
import re


class MatchStrategy(Enum):
    """匹配策略枚举"""
    AUTO_UPDATE = "auto_update"          # 自动更新
    SMART_RECOMMEND = "smart_recommend"  # 智能推荐
    MANUAL_CONFIRM = "manual_confirm"    # 人工确认
    NO_MATCH = "no_match"               # 无匹配


@dataclass
class MatchResult:
    """匹配结果数据类"""
    strategy: MatchStrategy
    confidence: float  # 匹配置信度 0-1
    matched_skus: List[Dict[str, Any]]  # 匹配的SKU列表
    recommended_sku: Optional[Dict[str, Any]] = None  # 推荐的SKU
    reason: str = ""  # 匹配原因说明
    action: str = ""  # 建议的操作
    cost_price: Optional[float] = None  # 建议的成本价
    color_groups: Optional[Dict[str, List[Dict[str, Any]]]] = None  # 颜色分组
    color_count: int = 0  # 颜色规格数量


@dataclass
class MatchConfig:
    """匹配配置"""
    # 价格相似度阈值
    price_similarity_threshold: float = 0.1  # 10%以内认为价格相似
    
    # 规格匹配关键词
    size_keywords: List[str] = None
    color_keywords: List[str] = None
    
    # 匹配置信度阈值
    auto_update_threshold: float = 0.9  # 90%以上自动更新
    recommend_threshold: float = 0.6    # 60%以上智能推荐
    
    def __post_init__(self):
        if self.size_keywords is None:
            self.size_keywords = ["S", "M", "L", "XL", "XXL", "小", "中", "大", "特大"]
        
        if self.color_keywords is None:
            self.color_keywords = ["红", "蓝", "黑", "白", "绿", "黄", "紫", "橙", "粉", "灰", "棕"]


class SKUMatcher:
    """SKU智能匹配器"""
    
    def __init__(self, config: Optional[MatchConfig] = None):
        self.config = config or MatchConfig()
        
    def group_skus_by_color_spec(self, erp_skus: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        按颜色规格分组SKU（排除尺码）

        Args:
            erp_skus: ERP中的SKU列表

        Returns:
            按颜色规格分组的SKU字典 {"白色上衣": [sku1, sku2, ...], "格纹半裙": [...]}
        """
        color_spec_groups = {}

        for sku in erp_skus:
            color = sku.get("color", "").strip()
            if color:
                # 使用颜色作为分组键
                if color not in color_spec_groups:
                    color_spec_groups[color] = []
                color_spec_groups[color].append(sku)

        return color_spec_groups

    def match_sku(self, ticket_item: Dict[str, Any], erp_skus: List[Dict[str, Any]]) -> MatchResult:
        """
        匹配单个票据商品与ERP SKU列表（按颜色规格分组）

        Args:
            ticket_item: 票据商品信息 {"sku_code": "GT408-3978", "spec": "红色-M", "price": 45.5, ...}
            erp_skus: ERP SKU列表

        Returns:
            MatchResult: 匹配结果
        """
        if not erp_skus:
            return MatchResult(
                strategy=MatchStrategy.NO_MATCH,
                confidence=0.0,
                matched_skus=[],
                reason="ERP中未找到相关SKU",
                action="请检查款号是否正确"
            )

        # 按颜色规格分组
        color_groups = self.group_skus_by_color_spec(erp_skus)
        color_count = len(color_groups)

        # 降级为DEBUG级别的print输出，这是内部匹配过程
        # print(f"🎨 按颜色规格分组：找到 {color_count} 个颜色规格")
        # for color, skus in color_groups.items():
        #     print(f"  - {color}: {len(skus)} 个尺码")

        # 在匹配结果中添加颜色分组信息
        base_result_data = {
            "color_groups": color_groups,
            "color_count": color_count
        }

        # 策略0: 单颜色规格优先处理（最高优先级）
        if color_count == 1:
            # 只有1个颜色规格，直接自动更新，无需其他匹配策略
            color_name = list(color_groups.keys())[0]
            color_skus = color_groups[color_name]

            return MatchResult(
                strategy=MatchStrategy.AUTO_UPDATE,
                confidence=0.90,  # 提高置信度
                matched_skus=color_skus,
                recommended_sku=color_skus[0],  # 选择第一个SKU作为代表
                color_groups=color_groups,
                color_count=color_count,
                reason=f"单颜色规格自动匹配: {color_name} ({len(color_skus)}个尺码)",
                action="批量更新所有尺码的成本价",
                cost_price=ticket_item.get("price", 0)  # 使用票据单价作为成本价
            )

        # 策略1: 精确规格匹配（多颜色规格时的最高优先级）
        exact_result = self._try_exact_spec_match(ticket_item, erp_skus)
        if exact_result.strategy == MatchStrategy.AUTO_UPDATE:
            # 添加颜色分组信息
            exact_result.color_groups = color_groups
            exact_result.color_count = color_count
            return exact_result

        # 策略2: 统一价格且颜色一致匹配
        uniform_result = self._try_uniform_price_match(ticket_item, erp_skus)
        if uniform_result.strategy == MatchStrategy.AUTO_UPDATE:
            # 添加颜色分组信息
            uniform_result.color_groups = color_groups
            uniform_result.color_count = color_count
            return uniform_result

        # 策略3: 相似度分析（仅用于推荐，不自动更新）
        similarity_result = self._try_similarity_match(ticket_item, erp_skus)
        if similarity_result.strategy == MatchStrategy.MANUAL_CONFIRM and similarity_result.recommended_sku:
            # 添加颜色分组信息
            similarity_result.color_groups = color_groups
            similarity_result.color_count = color_count
            return similarity_result

        # 如果以上策略都返回了MANUAL_CONFIRM，优先返回有推荐的结果
        if exact_result.strategy == MatchStrategy.MANUAL_CONFIRM:
            exact_result.color_groups = color_groups
            exact_result.color_count = color_count
            return exact_result
        if uniform_result.strategy == MatchStrategy.MANUAL_CONFIRM:
            uniform_result.color_groups = color_groups
            uniform_result.color_count = color_count
            return uniform_result

        # 默认情况：多颜色规格需要人工确认
        return MatchResult(
            strategy=MatchStrategy.MANUAL_CONFIRM,
            confidence=0.5,
            matched_skus=erp_skus,
            color_groups=color_groups,
            color_count=color_count,
            reason=f"待确认 - 找到{color_count}个颜色规格",
            action="需要人工确认选择正确的颜色规格"
        )
    
    def _try_uniform_price_match(self, ticket_item: Dict[str, Any], erp_skus: List[Dict[str, Any]]) -> MatchResult:
        """尝试统一价格匹配（根据ERP成本价更新逻辑文档）"""
        # 检查所有SKU的成本价是否相同
        cost_prices = [float(sku.get("cost_price", 0)) for sku in erp_skus]
        if len(set(cost_prices)) != 1:
            # 成本价不统一，不符合统一价格条件
            return MatchResult(MatchStrategy.NO_MATCH, 0.0, [])

        # 检查颜色一致性
        colors = [sku.get("color", "").lower() for sku in erp_skus]
        unique_colors = set(filter(None, colors))  # 过滤空字符串

        if len(unique_colors) == 1:
            # 所有SKU颜色相同且成本价相同，符合统一价格条件
            return MatchResult(
                strategy=MatchStrategy.AUTO_UPDATE,
                confidence=0.95,
                matched_skus=erp_skus,
                recommended_sku=erp_skus[0],  # 选择第一个SKU作为代表
                reason=f"统一价格且颜色一致，共{len(erp_skus)}个SKU",
                action="批量更新所有尺码的成本价",
                cost_price=cost_prices[0]
            )
        else:
            # 颜色不一致，需要人工确认
            return MatchResult(
                strategy=MatchStrategy.MANUAL_CONFIRM,
                confidence=0.5,
                matched_skus=erp_skus,
                reason=f"颜色不一致待确认 ({len(erp_skus)}个SKU)",
                action="需要人工确认选择正确的颜色"
            )
    
    def _try_exact_spec_match(self, ticket_item: Dict[str, Any], erp_skus: List[Dict[str, Any]]) -> MatchResult:
        """尝试精确规格匹配（根据ERP成本价更新逻辑文档）"""
        ticket_spec = ticket_item.get("spec", "").lower()
        if not ticket_spec:
            return MatchResult(MatchStrategy.NO_MATCH, 0.0, [])

        # 提取票据中的尺码和颜色
        ticket_size = self._extract_size(ticket_spec)
        ticket_colors = self._extract_colors(ticket_spec)

        if not ticket_size and not ticket_colors:
            return MatchResult(MatchStrategy.NO_MATCH, 0.0, [])

        # 查找精确匹配的SKU
        exact_matches = []
        for sku in erp_skus:
            sku_spec = sku.get("spec", "").lower()
            sku_color = sku.get("color", "").lower()
            sku_size = sku.get("size", "").lower()

            size_match = ticket_size and (ticket_size in sku_size or ticket_size in sku_spec)
            color_match = any(color in sku_color or color in sku_spec for color in ticket_colors)

            if size_match and color_match:
                exact_matches.append(sku)

        if len(exact_matches) == 1:
            # 唯一精确匹配，可以自动更新
            return MatchResult(
                strategy=MatchStrategy.AUTO_UPDATE,
                confidence=0.95,
                matched_skus=exact_matches,
                recommended_sku=exact_matches[0],
                reason=f"精确匹配: {ticket_spec}",
                action="自动更新成本价",
                cost_price=exact_matches[0].get("cost_price")
            )
        elif len(exact_matches) > 1:
            # 多个精确匹配，需要人工确认
            return MatchResult(
                strategy=MatchStrategy.MANUAL_CONFIRM,
                confidence=0.8,
                matched_skus=exact_matches,
                reason=f"多个精确匹配待确认 ({len(exact_matches)}个SKU)",
                action="需要人工确认选择正确的SKU"
            )

        return MatchResult(MatchStrategy.NO_MATCH, 0.0, [])
    
    def _try_similarity_match(self, ticket_item: Dict[str, Any], erp_skus: List[Dict[str, Any]]) -> MatchResult:
        """尝试相似度匹配（根据ERP成本价更新逻辑文档，提供推荐但不自动更新）"""
        ticket_price = float(ticket_item.get("price", 0))
        if ticket_price <= 0:
            return MatchResult(MatchStrategy.NO_MATCH, 0.0, [])

        # 计算每个SKU与票据单价的差值，用于推荐
        scored_skus = []
        for sku in erp_skus:
            sku_sale_price = float(sku.get("sale_price", 0))
            if sku_sale_price > 0:
                # 计算成本价与售价的差值
                price_diff = abs(ticket_price - sku_sale_price)
                # 计算毛利率
                margin_rate = (sku_sale_price - ticket_price) / sku_sale_price if sku_sale_price > 0 else 0

                # 评估合理性（毛利率在20%-70%范围内较合理）
                reasonableness = 1.0
                if margin_rate < 0.2 or margin_rate > 0.7:
                    reasonableness = 0.5  # 毛利率异常

                # 综合得分：差值越小越好，合理性越高越好
                score = reasonableness / (1 + price_diff / 10)  # 归一化得分
                scored_skus.append((sku, score, price_diff, margin_rate))

        if not scored_skus:
            return MatchResult(MatchStrategy.NO_MATCH, 0.0, [])

        # 按得分排序
        scored_skus.sort(key=lambda x: x[1], reverse=True)
        best_sku, best_score, best_diff, best_margin = scored_skus[0]

        # 根据ERP成本价更新逻辑文档，所有相似度匹配都标记为待确认
        # 但提供推荐信息帮助用户决策
        margin_info = f"毛利率{best_margin:.1%}"
        if best_margin < 0.2:
            margin_info += "(偏低)"
        elif best_margin > 0.7:
            margin_info += "(偏高)"

        return MatchResult(
            strategy=MatchStrategy.MANUAL_CONFIRM,
            confidence=best_score,
            matched_skus=[sku for sku, _, _, _ in scored_skus[:3]],  # 返回前3个最佳匹配
            recommended_sku=best_sku,
            reason=f"待确认(有推荐) - 推荐:{best_sku.get('spec', '')}, {margin_info}",
            action="需要人工确认",
            cost_price=best_sku.get("cost_price")
        )
    
    def _calculate_similarity_score(self, ticket_item: Dict[str, Any], sku: Dict[str, Any]) -> float:
        """计算相似度得分"""
        score = 0.0
        
        # 价格相似度 (权重: 0.4)
        ticket_price = float(ticket_item.get("price", 0))
        sku_price = float(sku.get("sale_price", 0))
        if ticket_price > 0 and sku_price > 0:
            price_diff = abs(ticket_price - sku_price) / ticket_price
            if price_diff <= self.config.price_similarity_threshold:
                score += 0.4 * (1 - price_diff / self.config.price_similarity_threshold)
        
        # 规格相似度 (权重: 0.6)
        ticket_spec = ticket_item.get("spec", "").lower()
        sku_spec = sku.get("spec", "").lower()
        sku_color = sku.get("color", "").lower()
        sku_size = sku.get("size", "").lower()
        
        if ticket_spec:
            # 颜色匹配
            ticket_colors = self._extract_colors(ticket_spec)
            if any(color in sku_color or color in sku_spec for color in ticket_colors):
                score += 0.3
            
            # 尺码匹配
            ticket_size = self._extract_size(ticket_spec)
            if ticket_size and (ticket_size in sku_size or ticket_size in sku_spec):
                score += 0.3
        
        return min(score, 1.0)  # 确保得分不超过1.0
    
    def _extract_colors(self, text: str) -> List[str]:
        """从文本中提取颜色"""
        colors = []
        for color in self.config.color_keywords:
            if color in text:
                colors.append(color)
        return colors
    
    def _extract_size(self, text: str) -> Optional[str]:
        """从文本中提取尺码"""
        for size in self.config.size_keywords:
            if size.lower() in text.lower():
                return size.lower()
        return None


def test_sku_matcher():
    """测试SKU匹配器"""
    print("🧪 测试SKU匹配器")
    print("=" * 50)
    
    matcher = SKUMatcher()
    
    # 测试数据
    ticket_item = {
        "sku_code": "GT408-3978",
        "spec": "红色-M",
        "price": 45.5
    }
    
    erp_skus = [
        {"sku_id": "GT408-3978-红色-S", "cost_price": 30.0, "sale_price": 45.5, "color": "红色", "size": "S", "spec": "红色-S"},
        {"sku_id": "GT408-3978-红色-M", "cost_price": 32.0, "sale_price": 45.5, "color": "红色", "size": "M", "spec": "红色-M"},
        {"sku_id": "GT408-3978-红色-L", "cost_price": 34.0, "sale_price": 45.5, "color": "红色", "size": "L", "spec": "红色-L"},
        {"sku_id": "GT408-3978-蓝色-M", "cost_price": 32.0, "sale_price": 48.0, "color": "蓝色", "size": "M", "spec": "蓝色-M"},
    ]
    
    result = matcher.match_sku(ticket_item, erp_skus)
    
    print(f"匹配策略: {result.strategy.value}")
    print(f"置信度: {result.confidence:.2f}")
    print(f"匹配原因: {result.reason}")
    print(f"建议操作: {result.action}")
    if result.recommended_sku:
        print(f"推荐SKU: {result.recommended_sku['sku_id']}")
        print(f"建议成本价: {result.cost_price}")
    
    return True


if __name__ == "__main__":
    test_sku_matcher()
