#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能图像分割处理模块
"""

import os
import json
from typing import List, Tu<PERSON>, Dict, Optional
from PIL import Image
from dataclasses import dataclass
import uuid

from datetime import datetime


@dataclass
class SplitPoint:
    """分割点信息"""
    y_position: int
    is_manual: bool = False
    confidence: float = 1.0


@dataclass
class ImageSegment:
    """图像段信息"""
    segment_id: str
    parent_id: str
    segment_index: int
    total_segments: int
    y_start: int
    y_end: int
    width: int
    height: int
    file_path: str
    supplier_code: str
    has_overlap_above: bool = False
    has_overlap_below: bool = False


@dataclass
class ImageInfo:
    """图像基本信息"""
    file_path: str
    width: int
    height: int
    size_mb: float
    needs_split: bool
    recommended_segments: int
    supplier_code: str = ""


class ImageSplitter:
    """智能图像分割器"""
    
    def __init__(self, max_height: int = 2000, overlap: int = 0, min_segment_height: int = 0):
        self.max_height = max_height
        self.overlap = overlap  # 设置为0，不再使用重叠
        self.min_segment_height = min_segment_height  # 设置为0，去掉高度限制
        self.split_threshold = max_height
        
    def analyze_image(self, image_path: str, supplier_code: str = "") -> ImageInfo:
        """分析图像基本信息"""
        try:
            img = Image.open(image_path)
            width, height = img.size
            
            # 计算文件大小
            file_size = os.path.getsize(image_path)
            size_mb = file_size / (1024 * 1024)
            
            # 判断是否需要分割 - 改为固定分割成3份
            needs_split = height > self.split_threshold
            
            # 固定推荐分段数为3
            recommended_segments = 3
                
            return ImageInfo(
                file_path=image_path,
                width=width,
                height=height,
                size_mb=size_mb,
                needs_split=needs_split,
                recommended_segments=recommended_segments,
                supplier_code=supplier_code
            )
            
        except Exception as e:
            raise Exception(f"图像分析失败: {str(e)}")
    
    def calculate_split_points(self, image_height: int) -> List[SplitPoint]:
        """计算自动分割点 - 固定分割成3份"""
        split_points = []
        
        # 固定分割成3份
        segment_height = image_height // 3
        
        # 第一个分割点
        split_y1 = segment_height
        split_points.append(SplitPoint(y_position=split_y1, is_manual=False))
        
        # 第二个分割点
        split_y2 = segment_height * 2
        split_points.append(SplitPoint(y_position=split_y2, is_manual=False))
            
        return split_points
    
    def preview_split(self, image_path: str, split_points: List[SplitPoint] = None) -> List[Dict]:
        """预览分割方案 - 去掉重叠区域逻辑"""
        img = Image.open(image_path)
        width, height = img.size
        
        if split_points is None:
            split_points = self.calculate_split_points(height)
            
        # 构建分段信息 - 不再添加重叠区域
        segments_preview = []
        y_positions = [0] + [sp.y_position for sp in split_points] + [height]
        
        for i in range(len(y_positions) - 1):
            y_start = y_positions[i]
            y_end = y_positions[i + 1]
                
            segment_height = y_end - y_start
            
            segments_preview.append({
                'index': i,
                'y_start': y_start,
                'y_end': y_end,
                'width': width,
                'height': segment_height,
                'has_overlap_above': False,  # 不再有重叠
                'has_overlap_below': False   # 不再有重叠
            })
            
        return segments_preview
    
    def split_image(self, image_path: str, supplier_code: str, 
                   split_points: List[SplitPoint] = None, 
                   output_dir: str = "temp_segments") -> List[ImageSegment]:
        """执行图像分割"""
        try:
            img = Image.open(image_path)
            width, height = img.size
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成父图像ID
            parent_id = str(uuid.uuid4())
            
            # 获取分割预览
            segments_preview = self.preview_split(image_path, split_points)
            
            # 执行分割
            segments = []
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            
            for i, segment_info in enumerate(segments_preview):
                y_start = segment_info['y_start']
                y_end = segment_info['y_end']
                
                # 裁剪图像
                segment_img = img.crop((0, y_start, width, y_end))
                
                # 生成文件名 - 使用简单的数字后缀，与原始逻辑保持一致
                segment_filename = f"{base_name}_{i+1}.jpg"
                segment_path = os.path.join(output_dir, segment_filename)
                
                # 保存图像
                segment_img.save(segment_path, 'JPEG', quality=95)
                
                # 创建段信息
                segment = ImageSegment(
                    segment_id=str(uuid.uuid4()),
                    parent_id=parent_id,
                    segment_index=i,
                    total_segments=len(segments_preview),
                    y_start=y_start,
                    y_end=y_end,
                    width=width,
                    height=y_end - y_start,
                    file_path=segment_path,
                    supplier_code=supplier_code,
                    has_overlap_above=False,  # 不再有重叠
                    has_overlap_below=False   # 不再有重叠
                )
                
                segments.append(segment)
                
            return segments
            
        except Exception as e:
            raise Exception(f"图像分割失败: {str(e)}")
    
    def validate_split_quality(self, segments: List[ImageSegment]) -> Dict:
        """验证分割质量 - 去掉高度限制检查"""
        issues = []
        warnings = []
        
        for segment in segments:
            # 去掉段高度检查
            # if segment.height < self.min_segment_height:
            #     issues.append(f"段 {segment.segment_index + 1} 高度过小: {segment.height}px")
                
            # 检查宽高比
            aspect_ratio = segment.height / segment.width
            if aspect_ratio > 15:
                warnings.append(f"段 {segment.segment_index + 1} 过于狭长 (比例: {aspect_ratio:.1f})")
                
        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'total_segments': len(segments)
        }


class SplitImageManager:
    """分割图像管理器"""
    
    def __init__(self, storage_file: str = "split_images.json"):
        self.storage_file = storage_file
        self.split_records = self.load_records()
        
    def load_records(self) -> Dict:
        """加载分割记录"""
        if os.path.exists(self.storage_file):
            try:
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def save_records(self):
        """保存分割记录"""
        with open(self.storage_file, 'w', encoding='utf-8') as f:
            json.dump(self.split_records, f, ensure_ascii=False, indent=2)
    
    def register_split(self, original_path: str, segments: List[ImageSegment]):
        """注册分割记录"""
        parent_id = segments[0].parent_id if segments else str(uuid.uuid4())
        
        record = {
            'parent_id': parent_id,
            'original_path': original_path,
            'supplier_code': segments[0].supplier_code if segments else "",
            'total_segments': len(segments),
            'created_time': datetime.now().isoformat(),
            'segments': []
        }
        
        for segment in segments:
            record['segments'].append({
                'segment_id': segment.segment_id,
                'segment_index': segment.segment_index,
                'file_path': segment.file_path,
                'y_start': segment.y_start,
                'y_end': segment.y_end,
                'width': segment.width,
                'height': segment.height,
                'has_overlap_above': segment.has_overlap_above,
                'has_overlap_below': segment.has_overlap_below
            })
            
        self.split_records[parent_id] = record
        self.save_records()
        
        return parent_id
    
    def get_split_info(self, parent_id: str) -> Optional[Dict]:
        """获取分割信息"""
        return self.split_records.get(parent_id)
    
    def get_segments_by_supplier(self, supplier_code: str) -> List[Dict]:
        """按供应商获取分割段"""
        segments = []
        for record in self.split_records.values():
            if record.get('supplier_code') == supplier_code:
                segments.extend(record.get('segments', []))
        return segments
    
    def cleanup_segments(self, parent_id: str):
        """清理分割段文件"""
        record = self.split_records.get(parent_id)
        if record:
            for segment_info in record.get('segments', []):
                file_path = segment_info.get('file_path')
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except:
                        pass
            
            # 删除记录
            del self.split_records[parent_id]
            self.save_records()
