# 智能票据处理系统 - 使用指南

## 🎯 快速入门

### 系统概述
智能票据处理系统是一个基于AI技术的自动化工具，能够识别票据图像中的商品信息，与ERP系统对接查询商品数据，并支持批量更新成本价。系统采用现代化界面设计，操作简单直观。

### 主要功能
- 🤖 **AI智能解析**: 自动识别票据中的商品信息
- 🔍 **ERP系统集成**: 实时查询商品价格和库存数据
- 💰 **成本价管理**: 智能计算利润并支持批量更新
- 🎨 **颜色确认**: 可视化颜色规格确认界面
- 📊 **退货率分析**: 智能计算和显示商品退货率

## 🚀 系统启动

### 方式一：双击启动（推荐）
1. 找到项目根目录下的 `启动程序.bat` 文件
2. 双击运行即可启动系统

### 方式二：命令行启动
1. 打开命令提示符（CMD）
2. 切换到项目目录：`cd "项目路径"`
3. 运行命令：`python main.py`

### 首次启动配置
系统首次启动时需要配置以下信息：
- **AI API密钥**: 用于图像识别功能
- **ERP认证信息**: 用于查询商品数据
- **供应商信息**: 可选配置

## 📋 基础操作流程

### 1. 上传票据图像

**步骤**：
1. 点击界面左上角的 **"选择文件"** 按钮
2. 在文件选择对话框中选择票据图像文件
3. 支持的格式：JPG、PNG、BMP、PDF等
4. 系统会自动显示图像预览

**注意事项**：
- 确保图像清晰，文字可读
- 建议图像分辨率不低于1000x1000像素
- 如果是长票据，可以使用图像分割功能

### 2. AI智能解析

**自动解析**：
1. 上传图像后，点击 **"开始解析"** 按钮
2. 系统会自动调用AI服务识别商品信息
3. 解析进度会在进度条中显示
4. 解析完成后，商品信息会显示在表格中

**解析结果包含**：
- 款号（SKU代码）
- 颜色规格
- 数量
- 单价
- 总价

### 3. ERP系统查询

**查询操作**：
1. 解析完成后，点击 **"查询ERP"** 按钮
2. 系统会自动查询每个款号的ERP信息
3. 查询进度会实时显示
4. 查询结果会更新到表格中

**查询结果显示**：
- 📈 **价格上涨**（红色）：ERP售价高于票据成本价
- 📉 **价格下降**（绿色）：ERP售价低于票据成本价
- ⚠️ **未找到**：ERP中没有对应商品
- 🔍 **查询中**：正在查询状态

### 4. 颜色规格确认

**确认流程**：
1. 点击表格中的 **"确认"** 按钮
2. 系统会弹出颜色确认面板
3. 面板显示该款号下的所有颜色规格
4. 每个颜色卡片显示：
   - 颜色名称
   - 售价信息
   - 退货率（30天）
   - 利润计算

**选择操作**：
1. 点击选择需要的颜色规格
2. 选中的卡片会有蓝色边框标识
3. 可以多选不同颜色
4. 点击 **"确认选择"** 完成确认

### 5. 成本价更新

**更新流程**：
1. 确认颜色规格后，返回主界面
2. 选择需要更新的商品行
3. 点击 **"更新成本价"** 按钮
4. 系统会批量更新选中商品的成本价

**更新策略**：
- 使用票据中的单价作为新的成本价
- 支持同一颜色规格下多个尺码的批量更新
- 自动选择最优的更新策略（串行/并发）

## 🎨 高级功能

### 图像分割功能

**使用场景**：
- 处理长票据图像
- 分离复杂布局的票据
- 提高AI识别准确率

**操作步骤**：
1. 上传图像后，点击 **"图像分割"** 按钮
2. 在分割界面中拖拽分割线
3. 调整分割区域
4. 点击 **"应用分割"** 生成分割后的图像
5. 对每个分割片段进行AI解析

### 批量文件处理

**功能说明**：
- 支持同时处理多个票据文件
- 每个文件独立维护状态
- 避免文件间数据污染

**操作方法**：
1. 使用文件管理器上传多个文件
2. 在文件列表中切换不同文件
3. 每个文件的处理状态独立保存
4. 可以并行处理多个文件

### 退货率分析

**功能特点**：
- 自动计算商品30天退货率
- 按商品链接级别统计
- 颜色标识风险等级

**颜色标识**：
- 🟢 **绿色**: 退货率 < 20%（低风险）
- 🟡 **橙色**: 20% ≤ 退货率 < 50%（中等风险）
- 🔴 **红色**: 退货率 ≥ 50%（高风险）

## ⚙️ 系统配置

### AI配置

**配置文件**: `user_config.json`

```json
{
    "ai_config": {
        "api_key": "你的AI API密钥",
        "base_url": "https://ark.cn-beijing.volces.com/api/v3",
        "model_name": "doubao-1.5-vision-lite-250315"
    }
}
```

**配置说明**：
- `api_key`: 从AI服务提供商获取的API密钥
- `base_url`: AI服务的API地址
- `model_name`: 使用的AI模型名称

### ERP配置

**认证方式**: Cookie认证

**配置步骤**：
1. 在浏览器中登录ERP系统
2. 使用开发者工具导出Cookie
3. 将Cookie保存为 `latest_cookies.json` 文件
4. 放置在项目根目录

**Cookie格式示例**：
```json
{
    "session_id": "xxx",
    "auth_token": "xxx",
    "user_id": "xxx"
}
```

### 供应商配置

**配置文件**: `suppliers.json`

```json
{
    "suppliers": [
        {
            "name": "供应商A",
            "code": "SUP001",
            "contact": "联系方式"
        }
    ]
}
```

## 🔧 故障排除

### 常见问题

**1. AI解析失败**
- **原因**: API密钥无效或网络问题
- **解决**: 检查配置文件中的API密钥，确保网络连接正常

**2. ERP查询失败**
- **原因**: Cookie过期或ERP系统维护
- **解决**: 更新Cookie文件，检查ERP系统状态

**3. 图像识别不准确**
- **原因**: 图像质量差或格式不支持
- **解决**: 使用高质量图像，尝试图像分割功能

**4. 成本价更新失败**
- **原因**: 权限不足或SKU不存在
- **解决**: 检查ERP用户权限，确认SKU信息正确

### 错误代码说明

| 错误代码 | 说明 | 解决方案 |
|----------|------|----------|
| AI_001 | API密钥无效 | 检查并更新API密钥 |
| AI_002 | 图像格式不支持 | 转换为支持的格式 |
| ERP_001 | 认证失效 | 更新Cookie文件 |
| ERP_002 | SKU不存在 | 检查SKU代码 |
| NET_001 | 网络连接失败 | 检查网络连接 |
| SYS_001 | 系统内部错误 | 重启程序或联系支持 |

### 性能优化建议

**1. 图像优化**
- 使用适当的图像分辨率（推荐1000-2000像素）
- 确保图像清晰度和对比度
- 避免过大的图像文件（建议<10MB）

**2. 网络优化**
- 使用稳定的网络连接
- 避免在网络高峰期进行大批量操作
- 适当调整超时设置

**3. 系统优化**
- 定期清理临时文件
- 关闭不必要的后台程序
- 确保足够的系统内存

## 📞 技术支持

### 联系方式
- **技术支持**: 一介哥专用AI助手
- **更新时间**: 2024年7月9日
- **版本**: v2.0.0

### 反馈渠道
如遇到问题或有改进建议，请通过以下方式反馈：
1. 详细描述问题现象
2. 提供错误截图或日志
3. 说明操作步骤和环境信息

### 更新日志
- **v2.0.0**: 重构架构，优化性能，新增退货率分析
- **v1.x.x**: 基础功能实现

---

**使用愉快！** 🎉
