"""
ERP查询线程 - 处理ERP查询和SKU匹配
"""

from PyQt5.QtCore import QThread, pyqtSignal


class ERPQueryThread(QThread):
    """ERP查询线程（带SKU匹配功能）"""
    progress_updated = pyqtSignal(int, int, str)  # 当前行, 总行数, 消息
    row_updated = pyqtSignal(int, str)           # 行号, ERP状态
    match_updated = pyqtSignal(int, str, str, float, str, dict)  # 行号, 匹配状态, 策略, 置信度, 推荐SKU_ID, 完整匹配结果
    query_completed = pyqtSignal(bool)           # 查询完成, 是否成功

    def __init__(self, table_data, erp_integration, sku_matcher, table_widget):
        super().__init__()
        self.table_data = table_data  # [(row, sku_code, spec, price), ...]
        self.erp_integration = erp_integration
        self.sku_matcher = sku_matcher
        self.table_widget = table_widget

    def run(self):
        """运行ERP查询"""
        try:
            total_rows = len(self.table_data)
            success_count = 0

            self.progress_updated.emit(0, total_rows, "开始ERP查询...")

            for i, (row, sku_code, spec, price) in enumerate(self.table_data):
                try:
                    # 检查线程是否被中断
                    if self.isInterruptionRequested():
                        self.progress_updated.emit(i, total_rows, "查询被中断")
                        break

                    self.progress_updated.emit(i + 1, total_rows, f"查询款号: {sku_code}")
                    self.row_updated.emit(row, "🔍 查询中...")

                    # 再次检查线程是否被中断（在网络请求前）
                    if self.isInterruptionRequested():
                        self.progress_updated.emit(i, total_rows, "查询被中断")
                        break

                    # 查询ERP
                    query_result = self.erp_integration.query_product_skus(sku_code)

                    if query_result['success']:
                        skus = query_result['data']
                        if len(skus) > 0:
                            # 检查线程是否被中断（在SKU匹配前）
                            if self.isInterruptionRequested():
                                self.progress_updated.emit(i, total_rows, "查询被中断")
                                break

                            # 进行SKU匹配分析，获取颜色规格分组信息
                            try:
                                self.progress_updated.emit(i + 1, total_rows, f"分析匹配: {sku_code}")

                                # 🔥 修复：改进价格数据处理，确保正确转换
                                ticket_price = 0.0
                                if price:
                                    try:
                                        # 清理价格格式（移除¥符号、逗号等）
                                        price_str = str(price).replace("¥", "").replace(",", "").strip()
                                        if price_str:
                                            ticket_price = float(price_str)
                                            print(f"🎯 价格转换成功: '{price}' -> {ticket_price}")
                                        else:
                                            print(f"⚠️ 价格为空字符串: '{price}'")
                                    except (ValueError, TypeError) as e:
                                        print(f"⚠️ 价格转换失败: '{price}' -> {str(e)}")
                                        ticket_price = 0.0
                                else:
                                    print(f"⚠️ 价格数据为空: {price}")

                                ticket_item = {
                                    "sku_code": sku_code,
                                    "spec": spec,
                                    "price": ticket_price
                                }

                                print(f"📋 构建ticket_item: {ticket_item}")

                                match_result = self.sku_matcher.match_sku(ticket_item, skus)

                                # 获取颜色规格数量
                                color_count = getattr(match_result, 'color_count', 0)
                                if color_count > 0:
                                    self.row_updated.emit(row, f"✅ 找到{color_count}个颜色规格")
                                else:
                                    self.row_updated.emit(row, f"✅ 找到{len(skus)}个SKU")
                                success_count += 1

                                # 发送匹配结果（传递完整的匹配信息）
                                match_status = self._format_match_status(match_result)
                                strategy = match_result.strategy.value if match_result else "no_match"
                                confidence = match_result.confidence if match_result else 0.0
                                recommended_sku_id = match_result.recommended_sku.get("sku_id", "") if match_result and match_result.recommended_sku else ""

                                # 创建完整的匹配结果数据
                                full_match_result = {
                                    "strategy": strategy,
                                    "confidence": confidence,
                                    "recommended_sku_id": recommended_sku_id,
                                    "status": match_status,
                                    "color_groups": getattr(match_result, 'color_groups', {}),
                                    "color_count": getattr(match_result, 'color_count', 0),
                                    "reason": getattr(match_result, 'reason', ''),
                                    "action": getattr(match_result, 'action', ''),
                                    "matched_skus": getattr(match_result, 'matched_skus', [])  # 添加匹配的SKU列表
                                }

                                self.match_updated.emit(row, match_status, strategy, confidence, recommended_sku_id, full_match_result)

                            except Exception as match_error:
                                self.row_updated.emit(row, f"✅ 找到{len(skus)}个SKU")
                                self.match_updated.emit(row, "⚠️ 匹配错误", "error", 0.0, "", {})
                                success_count += 1

                        else:
                            self.row_updated.emit(row, "❌ 无信息")
                            self.match_updated.emit(row, "❌ 无匹配", "no_match", 0.0, "", {})
                    else:
                        self.row_updated.emit(row, "⚠️ 查询失败")
                        self.match_updated.emit(row, "⚠️ 查询失败", "error", 0.0, "", {})

                    # 添加延时避免频率限制
                    self.msleep(500)  # 500毫秒延时

                except Exception as e:
                    try:
                        # 检查是否是网络相关错误
                        error_msg = str(e)
                        if "timeout" in error_msg.lower():
                            status = "⏰ 超时"
                        elif "connection" in error_msg.lower():
                            status = "🌐 连接失败"
                        else:
                            status = "⚠️ 查询错误"

                        self.row_updated.emit(row, status)
                        self.match_updated.emit(row, status, "error", 0.0, "", {})
                        # 降级为DEBUG级别，这是详细错误信息
                        self.progress_updated.emit(i + 1, total_rows, f"[DEBUG] 第{row+1}行{status}: {str(e)[:50]}")
                    except Exception as signal_error:
                        pass  # 避免信号发送失败导致的二次异常

            # 降级为DEBUG级别，这是查询完成信息
            self.progress_updated.emit(total_rows, total_rows, "[DEBUG] 查询完成")
            self.query_completed.emit(success_count > 0)

        except Exception as e:
            try:
                # 保留ERROR级别，这是重要错误信息
                self.progress_updated.emit(0, 0, f"查询过程出错: {str(e)}")
                self.query_completed.emit(False)
            except:
                pass  # 避免信号发送失败导致的二次异常

    def _format_match_status(self, match_result):
        """格式化匹配状态显示（直接显示价格比较结果）"""
        if not match_result:
            return "❌ 无匹配"

        from modules.sku_matcher import MatchStrategy
        
        # 获取颜色规格信息
        color_count = getattr(match_result, 'color_count', 0)
        color_info = f"({color_count}色)" if color_count > 0 else ""

        if match_result.strategy == MatchStrategy.AUTO_UPDATE:
            # 单个颜色规格：直接比较价格并显示结果
            if color_count == 1:
                return self._compare_and_format_price_status(match_result)
            else:
                # 多个颜色规格但价格统一：也直接比较价格
                return self._compare_and_format_price_status(match_result)
        elif match_result.strategy == MatchStrategy.SMART_RECOMMEND:
            return f"💡 智能推荐{color_info}"
        elif match_result.strategy == MatchStrategy.MANUAL_CONFIRM:
            return "🟡待确认"
        else:
            return "❌ 无匹配"

    def _compare_and_format_price_status(self, match_result):
        """比较价格并格式化状态显示"""
        try:
            # 获取票据价格（新成本价）
            ticket_price = getattr(match_result, 'cost_price', 0)
            if ticket_price == 0:
                return "⚠️ 价格错误"

            # 获取ERP当前成本价（从第一个SKU）
            matched_skus = getattr(match_result, 'matched_skus', [])
            if not matched_skus:
                return "⚠️ 无SKU信息"

            erp_cost_price = matched_skus[0].get("cost_price", None)

            # 比较价格
            if erp_cost_price is None or erp_cost_price == "" or erp_cost_price == 0:
                # ERP中没有成本价，这是首次设置
                return "✅ 新增"
            else:
                try:
                    erp_price_float = float(erp_cost_price)
                    ticket_price_float = float(ticket_price)
                    difference = ticket_price_float - erp_price_float

                    if abs(difference) < 0.01:
                        # 价格相同
                        return "✅ 无变化"
                    elif difference > 0.01:
                        # 价格上涨
                        return f"📈 ¥{difference:.1f}"
                    else:
                        # 价格下降
                        return f"📉 ¥{abs(difference):.1f}"
                except (ValueError, TypeError):
                    return "✅ 新增"  # 价格转换失败，默认显示新增

        except Exception as e:
            return "⚠️ 比较失败" 