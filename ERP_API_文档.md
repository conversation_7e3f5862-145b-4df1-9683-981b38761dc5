# ERP API 接口文档

## 📋 概述

本文档详细描述了智能票据处理系统与ERP系统的API接口规范，包括认证方式、接口定义、请求参数、响应格式等技术细节。

## 🔗 基础信息

### API基础配置
- **基础URL**: `https://apiweb.erp321.com/webapi`
- **认证方式**: Cookie认证
- **数据格式**: JSON
- **字符编码**: UTF-8
- **超时设置**: 连接超时5秒，读取超时15秒

### 系统参数
- **owner_co_id**: `13881863`
- **authorize_co_id**: `13881863`
- **uid**: `18707109`
- **coid**: `13881863`

## 🔐 认证机制

### Cookie认证
系统使用Cookie进行身份认证，需要在请求头中包含有效的认证Cookie。

**认证状态检查**：
```http
GET /webapi/UserApi/User/GetUserInfo
```

**响应示例**：
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "user_id": "18707109",
        "username": "用户名",
        "company_id": "13881863"
    }
}
```

### 认证失效处理
- **失效标识**: `code != 0` 或返回登录页面
- **处理策略**: 自动检测失效状态，提示用户更新Cookie
- **更新方式**: 手动导入新的Cookie文件

## 📊 商品查询接口

### 接口信息
- **接口路径**: `/ItemApi/ItemSku/GetPageListV2`
- **请求方法**: `POST`
- **内容类型**: `application/json; charset=utf-8`

### 请求参数

**URL参数**：
```
__from=web_component
owner_co_id=13881863
authorize_co_id=13881863
```

**请求体**：
```json
{
    "ip": "",
    "uid": "18707109",
    "coid": "13881863",
    "page": {
        "currentPage": 1,
        "pageSize": 50,
        "hasPageInfo": true,
        "pageAction": 1
    },
    "data": {
        "sku_type": 1,
        "has_daily_purchase_in_qty": false,
        "queryFlds": [
            "shop_sku_num", "pic", "i_id", "sku_id", "name", 
            "properties_value", "sale_price", "cost_price", 
            "supplier_name", "enabled", "modified", "created", 
            "formula_1", "sales_qty_7", "sent_qty_7", "as_qty_7",
            "sent_qty_15", "as_qty_15", "sent_qty_30", "as_qty_30", 
            "remark", "is_series_number", "c_id", "supplier_id", 
            "supplier_name", "pic_big"
        ],
        "orderBy": "",
        "enabled": "1",
        "i_id": "",
        "sku_id": "GD340-1011",
        "c_id": ""
    }
}
```

### 响应格式

**成功响应**：
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "sku_id": "GD340-1011-红色-S",
            "name": "时尚女装连衣裙",
            "properties_value": "红色,S",
            "cost_price": 45.50,
            "sale_price": 89.00,
            "supplier_name": "供应商A",
            "enabled": 1,
            "created": "2024-01-15 10:00:00",
            "modified": "2024-01-20 15:30:00",
            "sales_qty_7": 10,
            "sent_qty_7": 12,
            "as_qty_7": 2,
            "sent_qty_15": 25,
            "as_qty_15": 5,
            "sent_qty_30": 50,
            "as_qty_30": 8,
            "pic": "https://example.com/image.jpg",
            "remark": "备注信息"
        }
    ],
    "total": 1,
    "pageInfo": {
        "currentPage": 1,
        "pageSize": 50,
        "totalPages": 1,
        "totalCount": 1
    }
}
```

**错误响应**：
```json
{
    "code": 1001,
    "msg": "认证失效",
    "data": null
}
```

### 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| sku_id | String | SKU唯一标识符 |
| name | String | 商品名称 |
| properties_value | String | 规格属性（颜色,尺码） |
| cost_price | Number | 成本价 |
| sale_price | Number | 售价 |
| supplier_name | String | 供应商名称 |
| enabled | Number | 启用状态（1=启用，0=禁用） |
| created | String | 创建时间 |
| modified | String | 修改时间 |
| sales_qty_7 | Number | 7天销售数量 |
| sent_qty_7 | Number | 7天发货数量 |
| as_qty_7 | Number | 7天退货数量 |
| sent_qty_15 | Number | 15天发货数量 |
| as_qty_15 | Number | 15天退货数量 |
| sent_qty_30 | Number | 30天发货数量 |
| as_qty_30 | Number | 30天退货数量 |

## 💰 成本价更新接口

### 接口信息
- **接口路径**: `/ItemApi/ItemSku/UpdateCostPrice`
- **请求方法**: `POST`
- **内容类型**: `application/json; charset=utf-8`

### 请求参数

**URL参数**：
```
__from=web_component
owner_co_id=13881863
authorize_co_id=13881863
```

**请求体**：
```json
{
    "ip": "",
    "uid": "18707109",
    "coid": "13881863",
    "data": {
        "sku_id": "GD340-1011-红色-S",
        "cost_price": 48.50,
        "update_reason": "票据成本价更新"
    }
}
```

### 响应格式

**成功响应**：
```json
{
    "code": 0,
    "msg": "更新成功",
    "data": {
        "sku_id": "GD340-1011-红色-S",
        "old_cost_price": 45.50,
        "new_cost_price": 48.50,
        "update_time": "2024-01-20 16:00:00"
    }
}
```

**错误响应**：
```json
{
    "code": 1002,
    "msg": "SKU不存在或无权限修改",
    "data": null
}
```

## 🔄 批量操作

### 批量成本价更新
系统支持批量更新同一颜色规格下的多个尺码SKU，提高更新效率。

**策略选择**：
- **串行模式**: SKU数量 ≤ 3个，逐个更新
- **并发模式**: SKU数量 > 3个，并发更新（最大5个并发）

**延时策略**：
- **成功**: 延时0.5-1.0秒
- **失败**: 延时1.5-3.0秒
- **自适应**: 根据响应时间动态调整

## ⚠️ 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | 正常处理 |
| 1001 | 认证失效 | 更新Cookie重新认证 |
| 1002 | SKU不存在 | 检查SKU_ID是否正确 |
| 1003 | 无权限操作 | 检查用户权限 |
| 1004 | 参数错误 | 检查请求参数格式 |
| 1005 | 系统繁忙 | 稍后重试 |
| 2001 | 网络超时 | 检查网络连接 |
| 2002 | 服务器错误 | 联系技术支持 |

## 🚀 性能优化

### 请求优化
- **连接复用**: 使用Session保持连接
- **超时控制**: 合理设置连接和读取超时
- **重试机制**: 网络异常时自动重试
- **并发控制**: 限制并发请求数量

### 缓存策略
- **认证缓存**: 缓存认证状态，减少验证请求
- **查询缓存**: 短期缓存查询结果，避免重复请求
- **失效检测**: 智能检测缓存失效

### 错误处理
- **异常捕获**: 完整的异常处理机制
- **状态监控**: 实时监控API调用状态
- **日志记录**: 详细记录请求和响应日志

## 📝 使用示例

### Python示例
```python
import requests
import json

# 基础配置
base_url = "https://apiweb.erp321.com/webapi"
headers = {
    'Content-Type': 'application/json; charset=utf-8',
    'User-Agent': 'Mozilla/5.0...'
}

# 查询商品
def query_product(sku_code, cookies):
    url = f"{base_url}/ItemApi/ItemSku/GetPageListV2"
    params = {
        '__from': 'web_component',
        'owner_co_id': '13881863',
        'authorize_co_id': '13881863'
    }
    
    data = {
        "uid": "18707109",
        "coid": "13881863",
        "data": {
            "sku_id": sku_code,
            "enabled": "1"
        }
    }
    
    response = requests.post(
        url, 
        params=params, 
        headers=headers, 
        cookies=cookies,
        json=data,
        timeout=(5, 15)
    )
    
    return response.json()

# 更新成本价
def update_cost_price(sku_id, cost_price, cookies):
    url = f"{base_url}/ItemApi/ItemSku/UpdateCostPrice"
    params = {
        '__from': 'web_component',
        'owner_co_id': '13881863',
        'authorize_co_id': '13881863'
    }
    
    data = {
        "uid": "18707109",
        "coid": "13881863",
        "data": {
            "sku_id": sku_id,
            "cost_price": cost_price,
            "update_reason": "票据成本价更新"
        }
    }
    
    response = requests.post(
        url, 
        params=params, 
        headers=headers, 
        cookies=cookies,
        json=data,
        timeout=(5, 15)
    )
    
    return response.json()
```

## 🔧 调试工具

### 请求日志
系统提供详细的请求日志，包括：
- 请求URL和参数
- 请求头和Cookie
- 响应状态码和内容
- 响应时间统计

### 状态监控
- **认证状态**: 实时监控认证有效性
- **API状态**: 监控API响应状态
- **性能指标**: 统计响应时间和成功率

---

**文档版本**: v2.0.0  
**最后更新**: 2024年7月9日  
**维护团队**: 一介哥专用AI助手
