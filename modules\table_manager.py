"""
表格管理器 - 统一管理表格操作
"""

import os
from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor
from modules.ui_constants import UIConstants, StyleHelper
from modules.return_rate_calculator import ReturnRateCalculator


class TableManager:
    """表格管理器 - 统一处理表格操作"""
    
    def __init__(self, table_widget: QTableWidget, table_type: str = "pickup"):
        """
        初始化表格管理器

        Args:
            table_widget: QTableWidget实例
            table_type: 表格类型 ("pickup" 或 "return")
        """
        self.table = table_widget
        self.table_type = table_type
        self.return_rate_calculator = ReturnRateCalculator()
        self.setup_table()
    
    def setup_table(self):
        """设置表格基本属性"""
        # 设置列数和表头
        headers = UIConstants.Table.PICKUP_HEADERS if self.table_type == "pickup" else UIConstants.Table.RETURN_HEADERS
        self.table.setColumnCount(len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        # 设置为单选模式，只有按住Ctrl时才能多选
        self.table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.table.setSortingEnabled(False)
        
        # 设置列宽
        self._setup_column_widths()
        
        # 设置表头
        header = self.table.horizontalHeader()
        header.setStretchLastSection(False)  # 禁用自动拉伸
        header.setDefaultSectionSize(UIConstants.Table.COLUMN_WIDTH_MEDIUM)
        # 允许用户手动调整列宽
        for col in range(self.table.columnCount()):
            header.setSectionResizeMode(col, QHeaderView.Interactive)
        
        # 设置行高
        self.table.verticalHeader().setDefaultSectionSize(UIConstants.Table.ROW_HEIGHT)
        self.table.verticalHeader().setVisible(False)
    
    def _setup_column_widths(self):
        """设置列宽（优化为978px总宽度，11列布局，包含新增的30天实发列）"""
        # 强制设置表格总宽度（调整后的列宽）
        total_width = 40+120+80+80+80+80+100+80+106+106+106  # 978px
        self.table.setFixedWidth(total_width + 20)  # 加20px边距

        # 设置列调整模式：允许用户手动调整列宽
        header = self.table.horizontalHeader()
        header.setStretchLastSection(False)  # 确保最后一列不拉伸
        # 注意：这里不再设置Fixed模式，因为在setup_table中已经设置为Interactive

        column_widths = {
            0: UIConstants.Table.COLUMN_WIDTH_TINY,    # 行数 (40px)
            1: UIConstants.Table.COLUMN_WIDTH_LARGE,   # 款号 (120px)
            2: UIConstants.Table.COLUMN_WIDTH_XLARGE,  # 颜色规格 (80px)
            3: UIConstants.Table.COLUMN_WIDTH_MEDIUM,  # 数量 (80px)
            4: UIConstants.Table.COLUMN_WIDTH_MEDIUM,  # 单价 (80px)
            5: UIConstants.Table.COLUMN_WIDTH_MEDIUM,  # 小计 (80px)
            6: UIConstants.Table.COLUMN_WIDTH_STATUS,  # 状态 (100px)
            7: UIConstants.Table.COLUMN_WIDTH_MEDIUM,  # 利润 (80px)
            8: UIConstants.Table.COLUMN_WIDTH_SMALL,   # 15退货率 (106px)
            9: UIConstants.Table.COLUMN_WIDTH_SMALL,   # 30退货率 (106px)
            10: UIConstants.Table.COLUMN_WIDTH_SMALL,  # 30天实发 (106px)
        }
        # 总宽度: 40+120+140+80+80+80+150+80+70+70+70 = 980px

        for col, width in column_widths.items():
            if col < self.table.columnCount():
                self.table.setColumnWidth(col, width)

        # 延迟再次设置列宽，确保生效
        def delayed_column_setup():
            for col, width in column_widths.items():
                if col < self.table.columnCount():
                    self.table.setColumnWidth(col, width)

        from PyQt5.QtCore import QTimer
        QTimer.singleShot(200, delayed_column_setup)  # 比UI管理器晚一点
    
    def add_item_to_table(self, item_data: dict, row_number: int):
        """
        添加单个商品到表格
        
        Args:
            item_data: 商品数据字典
            row_number: 行号
        """
        try:
            row = self.table.rowCount()
            self.table.insertRow(row)
            
            # 设置行数
            self._set_table_item(row, 0, str(row_number), editable=False)
            
            # 设置款号列（可编辑）
            original_sku = str(item_data.get("款号", ""))
            processed_sku = self._process_sku_code(original_sku)
            self._set_table_item(row, 1, processed_sku, editable=True)
            
            # 设置其他列（不可编辑）
            self._set_table_item(row, 2, str(item_data.get("颜色规格", "")), editable=False)
            self._set_table_item(row, 3, str(item_data.get("数量", "")), editable=False)
            
            # 设置单价列（格式化显示）
            price = item_data.get("单价", "")
            formatted_price = StyleHelper.format_price_display(price)
            self._set_table_item(row, 4, formatted_price, editable=False)
            
            # 设置小计列
            self._set_table_item(row, 5, str(item_data.get("小计", "")), editable=False)
            
            # 设置状态列（初始为空）
            self._set_table_item(row, 6, "", editable=False)
            
            # 设置利润列（初始为空）
            self._set_table_item(row, 7, "-", editable=False)

            # 设置15退货率列（初始为空）
            self._set_table_item(row, 8, "-", editable=False)

            # 设置30退货率列（初始为空）
            self._set_table_item(row, 9, "-", editable=False)

            # 设置30天实发列（初始为空）
            self._set_table_item(row, 10, "-", editable=False)
            
        except Exception as e:
            # 降级为DEBUG级别的print输出
            # print(f"添加表格项失败: {str(e)}")
            pass
    
    def _set_table_item(self, row: int, col: int, text: str, editable: bool = False):
        """设置表格项"""
        item = QTableWidgetItem(text)
        item.setTextAlignment(Qt.AlignCenter)
        
        if editable:
            item.setFlags(item.flags() | Qt.ItemIsEditable)
        else:
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)
        
        self.table.setItem(row, col, item)
    
    def _process_sku_code(self, sku_code: str) -> str:
        """处理款号代码（应用正则处理和供应商前缀智能提取）"""
        import re

        # 1. 移除第二个短横线及其后的内容
        processed = re.sub(r'^([^-]+-[^-]+)-.*', r'\1', sku_code)

        # 2. 尝试智能添加供应商前缀（如果没有前缀的话）
        if not self._has_supplier_prefix(processed):
            supplier_code = self._extract_supplier_code_from_context()
            if supplier_code and not processed.startswith(f"{supplier_code}-"):
                processed = f"{supplier_code}-{processed}"
                print(f"款号处理: {sku_code} -> {processed} (添加供应商前缀: {supplier_code})")
            else:
                print(f"款号处理: {sku_code} -> {processed}")
        else:
            print(f"款号处理: {sku_code} -> {processed}")

        return processed

    def _has_supplier_prefix(self, sku_code: str) -> bool:
        """检查款号是否已有供应商前缀"""
        import re
        # 检查是否符合供应商前缀格式：字母+数字-款号
        pattern = r'^[A-Z]{2,6}\d{2,4}-'
        return bool(re.match(pattern, sku_code))

    def _extract_supplier_code_from_context(self) -> str:
        """从上下文中提取供应商代码"""
        try:
            # 已知的供应商代码列表（从重构前的代码中提取）
            supplier_codes = [
                "GT408", "GT251", "GT155", "GT253", "GT158", "GTAF05",
                "DSD258", "DSD129", "DSD106", "GD340"
            ]

            # 1. 尝试从当前选中的供应商获取
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'supplier_combo'):
                current_supplier = self.main_window.supplier_combo.currentText()
                for code in supplier_codes:
                    if code in current_supplier:
                        return code

            # 2. 尝试从当前处理的文件名中提取
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'image_manager'):
                current_image = self.main_window.image_manager.current_image_path
                if current_image:
                    filename = os.path.basename(current_image).upper()
                    for code in supplier_codes:
                        if filename.startswith(code):
                            return code

            # 3. 尝试从重命名缓存中获取
            if (hasattr(self, 'main_window') and
                hasattr(self.main_window, 'image_rename_cache') and
                hasattr(self.main_window, 'image_manager')):
                current_image = self.main_window.image_manager.current_image_path
                if current_image and current_image in self.main_window.image_rename_cache:
                    cache_data = self.main_window.image_rename_cache[current_image]
                    supplier_code = cache_data.get('supplier_code', '')
                    if supplier_code:
                        return supplier_code

            return ""
        except Exception as e:
            print(f"提取供应商代码失败: {str(e)}")
            return ""
    
    def set_erp_status(self, row: int, status: str):
        """设置ERP状态"""
        try:
            if row < self.table.rowCount():
                item = self.table.item(row, 6)  # 状态列
                if item:
                    item.setText(status)
                    item.setTextAlignment(Qt.AlignCenter)
                    self._set_status_style(item, status)
                else:
                    item = QTableWidgetItem(status)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    self._set_status_style(item, status)
                    self.table.setItem(row, 6, item)
        except Exception as e:
            # 降级为DEBUG级别的print输出
            # print(f"设置ERP状态失败: {str(e)}")
            pass
    
    def set_combined_status(self, row: int, match_status: str = None, erp_status: str = None):
        """设置合并状态（ERP状态和匹配状态）"""
        try:
            if row < self.table.rowCount():
                # 优先显示匹配状态，如果没有则显示ERP状态
                status = match_status if match_status else erp_status
                if status:
                    self.set_erp_status(row, status)
        except Exception as e:
            print(f"设置合并状态失败: {str(e)}")
    
    def _set_status_style(self, item: QTableWidgetItem, status: str):
        """设置状态样式"""
        color = StyleHelper.create_status_color(status)
        item.setForeground(QColor(color))
        # 🔥 修改：确保价格变化状态使用正确的字体颜色
        if "📈" in status:
            item.setForeground(QColor("#ff3e66"))  # 统一红色字体（价格上涨）
        elif "📉" in status:
            item.setForeground(QColor(76, 175, 80))  # 绿色字体（价格下降）
    
    def set_profit(self, row: int, sale_price, cost_price):
        """设置利润显示"""
        try:
            if row < self.table.rowCount():
                profit_text, color = StyleHelper.format_profit_display(sale_price, cost_price)

                item = self.table.item(row, 7)  # 利润列
                if item:
                    item.setText(profit_text)
                else:
                    item = QTableWidgetItem(profit_text)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    self.table.setItem(row, 7, item)

                item.setForeground(QColor(color))
        except Exception as e:
            print(f"设置利润失败: {str(e)}")

    def set_return_rate(self, row: int, skus: list):
        """设置退货率显示和30天实发（三列格式：15退货率、30退货率、30天实发）

        Args:
            row: 表格行号
            skus: 同一款号下的SKU列表，用于计算退货率和实发数量
        """
        try:
            if row < self.table.rowCount():
                # 使用退货率计算器计算退货率
                return_rates = self.return_rate_calculator.calculate_return_rates_by_sku_code(skus)
                formatted_rates = self.return_rate_calculator.format_return_rate_integer(return_rates)

                # 设置两列退货率：15退货率(第8列)、30退货率(第9列)
                periods = ["15天", "30天"]
                for i, period in enumerate(periods):
                    col_index = 8 + i  # 第8、9列
                    rate_text = formatted_rates[period]

                    item = self.table.item(row, col_index)
                    if item:
                        item.setText(rate_text)
                    else:
                        item = QTableWidgetItem(rate_text)
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                        self.table.setItem(row, col_index, item)

                    # 设置颜色（基于退货率）
                    if rate_text != "-" and "|" not in rate_text:
                        try:
                            rate_value = float(rate_text.replace("%", ""))
                            if rate_value < 20:
                                color = "#4CAF50"  # 绿色
                            elif rate_value < 50:
                                color = "#FF9800"  # 橙色
                            else:
                                color = "#ff3e66"  # 统一红色
                            item.setForeground(QColor(color))
                        except:
                            pass

                # 使用商品链接级别的30天实发计算

                # 使用商品链接级别的计算
                link_rates = self.return_rate_calculator.calculate_return_rates_by_product_links(skus)
                sent_qty_text = self.return_rate_calculator.format_multi_link_sent_qty_30_display(link_rates)

                sent_item = self.table.item(row, 10)
                if sent_item:
                    sent_item.setText(sent_qty_text)
                else:
                    sent_item = QTableWidgetItem(sent_qty_text)
                    sent_item.setTextAlignment(Qt.AlignCenter)
                    sent_item.setFlags(sent_item.flags() & ~Qt.ItemIsEditable)
                    sent_item.setForeground(QColor(255, 255, 255))  # 白色字体
                    self.table.setItem(row, 10, sent_item)
        except Exception as e:
            print(f"设置退货率失败: {str(e)}")

    def set_return_rate_direct(self, row: int, return_rates: dict):
        """
        直接设置退货率显示（两列格式：15退货率、30退货率）

        Args:
            row: 表格行号
            return_rates: 退货率字典，格式: {"15天": 75.5, "30天": 68.2}
        """
        try:
            if row < self.table.rowCount():
                formatted_rates = self.return_rate_calculator.format_return_rate_integer(return_rates)

                # 设置两列退货率：15退货率(第8列)、30退货率(第9列)
                periods = ["15天", "30天"]
                for i, period in enumerate(periods):
                    col_index = 8 + i  # 第8、9列
                    rate_text = formatted_rates[period]

                    item = self.table.item(row, col_index)
                    if item:
                        item.setText(rate_text)
                    else:
                        item = QTableWidgetItem(rate_text)
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                        self.table.setItem(row, col_index, item)

                    # 设置颜色（基于各自的退货率值）
                    if rate_text != "-":
                        try:
                            rate_value = float(rate_text.replace("%", ""))
                            if rate_value < 20:
                                color = "#4CAF50"  # 绿色
                            elif rate_value < 50:
                                color = "#FF9800"  # 橙色
                            else:
                                color = "#ff3e66"  # 统一红色
                            item.setForeground(QColor(color))
                        except:
                            pass
        except Exception as e:
            print(f"设置退货率失败: {str(e)}")

    def set_return_rate_by_product_links(self, row: int, skus: list):
        """
        设置商品链接级别的退货率和30天实发（三列格式：15退货率、30退货率、30天实发）

        Args:
            row: 表格行号
            skus: SKU列表，用于识别商品链接并计算退货率和实发数量
        """
        try:
            if row < self.table.rowCount():


                # 使用增强的退货率计算器
                from modules.return_rate_calculator import ReturnRateCalculator
                calculator = ReturnRateCalculator()
                link_rates = calculator.calculate_return_rates_by_product_links(skus)
                formatted_rates = calculator.format_multi_link_display(link_rates)

                # 设置两列退货率：15退货率(第8列)、30退货率(第9列)
                periods = ["15天", "30天"]
                for i, period in enumerate(periods):
                    col_index = 8 + i  # 第8、9列
                    rate_text = formatted_rates[period]

                    item = self.table.item(row, col_index)
                    if item:
                        item.setText(rate_text)
                    else:
                        item = QTableWidgetItem(rate_text)
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                        self.table.setItem(row, col_index, item)

                    # 设置颜色（基于30天退货率，只对单链接设置颜色）
                    if period == "30天" and rate_text != "-" and "|" not in rate_text:
                        try:
                            rate_value = float(rate_text.replace("%", ""))
                            if rate_value < 20:
                                color = "#4CAF50"  # 绿色
                            elif rate_value < 50:
                                color = "#FF9800"  # 橙色
                            else:
                                color = "#ff3e66"  # 统一红色
                            item.setForeground(QColor(color))
                        except:
                            pass
                    elif period == "15天" and rate_text != "-" and "|" not in rate_text:
                        # 15天也使用相同的颜色逻辑
                        try:
                            rate_value = float(rate_text.replace("%", ""))
                            if rate_value < 20:
                                color = "#4CAF50"  # 绿色
                            elif rate_value < 50:
                                color = "#FF9800"  # 橙色
                            else:
                                color = "#ff3e66"  # 统一红色
                            item.setForeground(QColor(color))
                        except:
                            pass

                # 使用与退货率相同的多链接显示逻辑
                sent_qty_text = calculator.format_multi_link_sent_qty_30_display(link_rates)

                sent_item = self.table.item(row, 10)
                if sent_item:
                    sent_item.setText(sent_qty_text)
                else:
                    sent_item = QTableWidgetItem(sent_qty_text)
                    sent_item.setTextAlignment(Qt.AlignCenter)
                    sent_item.setFlags(sent_item.flags() & ~Qt.ItemIsEditable)
                    sent_item.setForeground(QColor(255, 255, 255))  # 白色字体
                    self.table.setItem(row, 10, sent_item)


        except Exception as e:
            print(f"设置商品链接退货率失败: {str(e)}")

    def clear_table(self):
        """清空表格"""
        self.table.setRowCount(0)
    
    def get_table_data(self):
        """获取表格数据"""
        data = []
        for row in range(self.table.rowCount()):
            row_data = {}
            headers = UIConstants.Table.PICKUP_HEADERS if self.table_type == "pickup" else UIConstants.Table.RETURN_HEADERS
            
            for col, header in enumerate(headers):
                item = self.table.item(row, col)
                row_data[header] = item.text() if item else ""
            
            data.append(row_data)
        
        return data
    
    def restore_table_data(self, data_list: list):
        """恢复表格数据"""
        try:
            self.clear_table()
            
            for i, item_data in enumerate(data_list):
                self.add_item_to_table(item_data, i + 1)
                
                # 恢复状态和利润信息
                if "状态" in item_data and item_data["状态"]:
                    self.set_erp_status(i, item_data["状态"])

                if "利润" in item_data and item_data["利润"] != "-":
                    profit_item = QTableWidgetItem(item_data["利润"])
                    profit_item.setTextAlignment(Qt.AlignCenter)
                    profit_item.setFlags(profit_item.flags() & ~Qt.ItemIsEditable)

                    # 设置利润颜色
                    if "¥" in item_data["利润"]:
                        try:
                            profit_value = float(item_data["利润"].replace("¥", ""))
                            color = UIConstants.Colors.DANGER if profit_value < 20 else UIConstants.Colors.SUCCESS
                            profit_item.setForeground(QColor(color))
                        except:
                            pass

                    self.table.setItem(i, 7, profit_item)

                # 恢复退货率信息（三列格式）
                # 处理旧格式的退货率数据（兼容性）
                if "退货率" in item_data and item_data["退货率"] != "-":
                    old_rate_text = item_data["退货率"]
                    # 尝试解析旧格式的退货率数据
                    try:
                        if "7天:" in old_rate_text and "15天:" in old_rate_text and "30天:" in old_rate_text:
                            # 解析格式如 "7天:82.9% | 15天:75.5% | 30天:68.2%"
                            parts = old_rate_text.split(" | ")
                            rates = {}
                            for part in parts:
                                if ":" in part and "%" in part:
                                    period = part.split(":")[0]
                                    rate_str = part.split(":")[1].replace("%", "")
                                    rates[period] = float(rate_str)

                            # 使用新的两列格式设置
                            formatted_rates = self.return_rate_calculator.format_return_rate_integer(rates)
                            periods = ["15天", "30天"]
                            for j, period in enumerate(periods):
                                col_index = 8 + j  # 第8、9列
                                rate_text = formatted_rates[period]

                                return_rate_item = QTableWidgetItem(rate_text)
                                return_rate_item.setTextAlignment(Qt.AlignCenter)
                                return_rate_item.setFlags(return_rate_item.flags() & ~Qt.ItemIsEditable)

                                # 设置颜色
                                if rate_text != "-":
                                    try:
                                        rate_value = float(rate_text.replace("%", ""))
                                        if rate_value < 20:
                                            color = "#4CAF50"  # 绿色
                                        elif rate_value < 50:
                                            color = "#FF9800"  # 橙色
                                        else:
                                            color = "#ff3e66"  # 统一红色
                                        return_rate_item.setForeground(QColor(color))
                                    except:
                                        pass

                                self.table.setItem(i, col_index, return_rate_item)
                    except:
                        # 如果解析失败，设置默认值
                        for j in range(2):  # 只有2列退货率
                            col_index = 8 + j
                            return_rate_item = QTableWidgetItem("-")
                            return_rate_item.setTextAlignment(Qt.AlignCenter)
                            return_rate_item.setFlags(return_rate_item.flags() & ~Qt.ItemIsEditable)
                            self.table.setItem(i, col_index, return_rate_item)

                        # 设置30天实发列默认值
                        sent_item = QTableWidgetItem("-")
                        sent_item.setTextAlignment(Qt.AlignCenter)
                        sent_item.setFlags(sent_item.flags() & ~Qt.ItemIsEditable)
                        sent_item.setForeground(QColor(255, 255, 255))
                        self.table.setItem(i, 10, sent_item)

                # 处理新格式的退货率数据
                for j, period_key in enumerate(["15退货率", "30退货率"]):
                    if period_key in item_data and item_data[period_key] != "-":
                        col_index = 8 + j
                        rate_text = item_data[period_key]

                        return_rate_item = QTableWidgetItem(rate_text)
                        return_rate_item.setTextAlignment(Qt.AlignCenter)
                        return_rate_item.setFlags(return_rate_item.flags() & ~Qt.ItemIsEditable)

                        # 设置颜色
                        if rate_text != "-":
                            try:
                                rate_value = float(rate_text.replace("%", ""))
                                if rate_value < 20:
                                    color = "#4CAF50"  # 绿色
                                elif rate_value < 50:
                                    color = "#FF9800"  # 橙色
                                else:
                                    color = "#ff3e66"  # 统一红色
                                return_rate_item.setForeground(QColor(color))
                            except:
                                pass

                        self.table.setItem(i, col_index, return_rate_item)

                # 处理30天实发数据
                if "30天实发" in item_data and item_data["30天实发"] != "-":
                    sent_text = item_data["30天实发"]
                    sent_item = QTableWidgetItem(sent_text)
                    sent_item.setTextAlignment(Qt.AlignCenter)
                    sent_item.setFlags(sent_item.flags() & ~Qt.ItemIsEditable)
                    sent_item.setForeground(QColor(255, 255, 255))  # 白色字体
                    self.table.setItem(i, 10, sent_item)
                else:
                    # 设置默认值
                    sent_item = QTableWidgetItem("-")
                    sent_item.setTextAlignment(Qt.AlignCenter)
                    sent_item.setFlags(sent_item.flags() & ~Qt.ItemIsEditable)
                    sent_item.setForeground(QColor(255, 255, 255))
                    self.table.setItem(i, 10, sent_item)

        except Exception as e:
            print(f"恢复表格数据失败: {str(e)}")
    
    def get_selected_rows(self):
        """获取选中的行"""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())
        return list(selected_rows)
    
    def get_row_data(self, row: int):
        """获取指定行的数据"""
        if row >= self.table.rowCount():
            return None
        
        headers = UIConstants.Table.PICKUP_HEADERS if self.table_type == "pickup" else UIConstants.Table.RETURN_HEADERS
        row_data = {}
        
        for col, header in enumerate(headers):
            item = self.table.item(row, col)
            row_data[header] = item.text() if item else ""
        
        return row_data
    
    def update_row_data(self, row: int, column: int, value: str):
        """更新指定行列的数据"""
        try:
            if row < self.table.rowCount() and column < self.table.columnCount():
                item = self.table.item(row, column)
                if item:
                    item.setText(value)
                else:
                    self._set_table_item(row, column, value)
        except Exception as e:
            # 降级为DEBUG级别的print输出
            # print(f"更新行数据失败: {str(e)}")
            pass


class TableOperationHelper:
    """表格操作辅助类"""
    
    @staticmethod
    def remove_duplicate_items(items: list) -> list:
        """去重处理"""
        seen = set()
        unique_items = []
        
        for item in items:
            # 创建唯一标识符
            identifier = f"{item.get('款号', '')}-{item.get('颜色规格', '')}-{item.get('数量', '')}-{item.get('单价', '')}"
            
            if identifier not in seen:
                seen.add(identifier)
                unique_items.append(item)
        
        return unique_items
    
    @staticmethod
    def classify_items(items: list) -> tuple:
        """分类商品为拿货和退货"""
        pickup_items = []
        return_items = []
        
        for item in items:
            try:
                quantity = float(item.get("数量", 0))
                if quantity < 0:
                    return_items.append(item)
                else:
                    pickup_items.append(item)
            except:
                pickup_items.append(item)  # 默认归类为拿货
        
        return pickup_items, return_items
    
    @staticmethod
    def extract_table_query_data(table_manager: TableManager) -> list:
        """提取表格查询数据"""
        query_data = []
        
        for row in range(table_manager.table.rowCount()):
            sku_item = table_manager.table.item(row, 1)  # 款号列
            spec_item = table_manager.table.item(row, 2)  # 颜色规格列
            price_item = table_manager.table.item(row, 4)  # 单价列
            
            if sku_item and sku_item.text().strip():
                sku_code = sku_item.text().strip()
                spec = spec_item.text().strip() if spec_item else ""
                price = price_item.text().strip() if price_item else "0"
                
                # 清理价格格式
                if "-100=" in price:
                    price = price.split("=")[1]
                price = price.replace("¥", "").strip()
                
                query_data.append((row, sku_code, spec, price))
        
        return query_data
