"""
ERP UI管理器 - 负责ERP查询、成本更新等功能的UI交互
重构自原始pyqt5_main_gui.py的ERP处理部分
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import os

from modules.threads import ERPQueryThread, CostUpdateThread
from modules.erp_integration import ERPIntegration
from modules.sku_matcher import SKUMatcher


class ERPUIManager:
    """ERP UI管理器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        
        # 初始化核心组件
        self.erp_integration = ERPIntegration()
        self.sku_matcher = SKUMatcher()
        
        # 线程管理
        self.erp_query_thread = None
        self.cost_update_thread = None
        
        # 处理状态
        self.processing = False

    def query_erp_for_table(self, table_type):
        """查询表格的ERP信息"""
        if self.processing:
            self.main_window.log_message("正在处理中，请稍候...", "WARNING")
            return

        table = self.main_window.table_ui_manager.get_table_by_type(table_type)
        if not table or table.rowCount() == 0:
            self.main_window.log_message("表格为空，无需查询", "WARNING")
            return

        # 检查ERP配置
        if not self.erp_integration.is_configured():
            self.main_window.log_message("请先配置ERP连接", "ERROR")
            return

        # 收集表格数据
        table_data = []
        for row in range(table.rowCount()):
            try:
                sku_code_item = table.item(row, 1)  # 款号
                spec_item = table.item(row, 2)      # 颜色规格
                price_item = table.item(row, 4)     # 单价
                
                if sku_code_item and sku_code_item.text().strip():
                    sku_code = sku_code_item.text().strip()
                    spec = spec_item.text().strip() if spec_item else ""

                    # 🔥 修复：检查单价列是否有有效数据
                    if price_item and price_item.text().strip():
                        price = price_item.text().strip()
                        print(f"📋 获取表格数据 - 行{row+1}: 款号={sku_code}, 规格={spec}, 单价={price}")
                        table_data.append((row, sku_code, spec, price))
                    else:
                        print(f"⚠️ 跳过第{row+1}行：单价列为空 - 款号={sku_code}")
                        self.main_window.log_message(f"跳过第{row+1}行：单价列为空", "WARNING")
            except Exception as e:
                self.main_window.log_message(f"获取第{row+1}行数据失败: {str(e)}", "ERROR")

        if not table_data:
            self.main_window.log_message("没有有效的商品数据", "WARNING")
            return

        # 启动ERP查询线程
        self.start_erp_query_thread(table_data, table_type)

    def query_erp_for_selected_rows(self, table_type):
        """查询选中行的ERP信息"""
        if self.processing:
            self.main_window.log_message("正在处理中，请稍候...", "WARNING")
            return

        table = self.main_window.table_ui_manager.get_table_by_type(table_type)
        if not table:
            return

        # 获取选中的行
        selected_rows = table.selectionModel().selectedRows()
        if not selected_rows:
            self.main_window.log_message("请先选中要查询的行", "WARNING")
            return

        # 检查ERP配置
        if not self.erp_integration.is_configured():
            self.main_window.log_message("请先配置ERP连接", "ERROR")
            return

        # 收集选中行的数据
        table_data = []
        for selected_row in selected_rows:
            row = selected_row.row()
            try:
                sku_code_item = table.item(row, 1)  # 款号
                spec_item = table.item(row, 2)      # 颜色规格
                price_item = table.item(row, 4)     # 单价
                
                if sku_code_item and sku_code_item.text().strip():
                    sku_code = sku_code_item.text().strip()
                    spec = spec_item.text().strip() if spec_item else ""

                    # 🔥 修复：检查单价列是否有有效数据
                    if price_item and price_item.text().strip():
                        price = price_item.text().strip()
                        print(f"📋 获取选中行数据 - 行{row+1}: 款号={sku_code}, 规格={spec}, 单价={price}")
                        table_data.append((row, sku_code, spec, price))
                    else:
                        print(f"⚠️ 跳过选中行{row+1}：单价列为空 - 款号={sku_code}")
                        self.main_window.log_message(f"跳过选中行{row+1}：单价列为空", "WARNING")
            except Exception as e:
                self.main_window.log_message(f"获取第{row+1}行数据失败: {str(e)}", "ERROR")

        if not table_data:
            self.main_window.log_message("没有有效的选中数据", "WARNING")
            return

        # 启动ERP查询线程
        self.start_erp_query_thread(table_data, table_type, selected_only=True)

    def start_erp_query_thread(self, table_data, table_type, selected_only=False):
        """启动ERP查询线程"""
        try:
            table = self.main_window.table_ui_manager.get_table_by_type(table_type)
            
            # 创建查询线程
            self.erp_query_thread = ERPQueryThread(
                table_data, 
                self.erp_integration, 
                self.sku_matcher, 
                table
            )
            
            # 连接信号
            self.erp_query_thread.progress_updated.connect(self._on_erp_query_progress)
            self.erp_query_thread.row_updated.connect(self._on_row_status_updated)
            self.erp_query_thread.match_updated.connect(self._on_match_result_updated)
            
            if selected_only:
                self.erp_query_thread.query_completed.connect(
                    lambda success: self._on_erp_query_selected_completed(success, table_type)
                )
            else:
                self.erp_query_thread.query_completed.connect(self._on_erp_query_completed)
            
            # 更新UI状态
            self.processing = True
            self._update_button_states(table_type, querying=True)
            
            # 启动线程
            self.erp_query_thread.start()

            # 显示统一进度条
            self.main_window.show_unified_progress("查询ERP中...")

            query_type = "选中商品" if selected_only else f"{table_type}表格"
            self.main_window.log_message(f"开始查询{query_type}的ERP信息")
            self.main_window.log_message(f"正在查询{len(table_data)}个商品的ERP信息...")
            
        except Exception as e:
            self.main_window.log_message(f"启动ERP查询失败: {str(e)}", "ERROR")
            self.processing = False

    def update_erp_costs_for_table(self, table_type):
        """更新表格的ERP成本价"""
        if self.processing:
            self.main_window.log_message("正在处理中，请稍候...", "WARNING")
            return

        table = self.main_window.table_ui_manager.get_table_by_type(table_type)
        if not table or table.rowCount() == 0:
            self.main_window.log_message("表格为空，无需更新", "WARNING")
            return

        # 检查ERP配置
        if not self.erp_integration.is_configured():
            self.main_window.log_message("请先配置ERP连接", "ERROR")
            return

        # 收集需要更新的商品
        update_items = []
        for row in range(table.rowCount()):
            try:
                status_item = table.item(row, 6)  # 状态列
                if status_item:
                    status_text = status_item.text()
                    # 🔥 修复：允许多种状态进入更新流程
                    # 包括：✅(查询成功)、📈(价格上涨)、📉(价格下降)、🆕(新增)
                    if any(symbol in status_text for symbol in ["✅", "📈", "📉", "🆕"]):
                        print(f"🔍 第{row+1}行状态检查通过：{status_text}")

                        sku_code_item = table.item(row, 1)
                        price_item = table.item(row, 4)

                        if sku_code_item and price_item:
                            sku_code = sku_code_item.text().strip()
                            ticket_price = price_item.text().strip().replace('¥', '').replace(',', '')

                            # 🔥 修复：从ERP匹配结果中获取真实的SKU信息
                            if hasattr(self.main_window, 'erp_match_results') and row in self.main_window.erp_match_results:
                                match_result = self.main_window.erp_match_results[row]
                                strategy = match_result.get('strategy', 'manual_confirm')
                                color_groups = match_result.get('color_groups', {})

                                # 🔥 修复：改进"无变化"状态的处理逻辑
                                if "无变化" in status_text:
                                    # 检查是否是强制更新模式（可以通过按键修饰符判断）
                                    from PyQt5.QtWidgets import QApplication
                                    modifiers = QApplication.keyboardModifiers()
                                    from PyQt5.QtCore import Qt

                                    if modifiers & Qt.ControlModifier:
                                        # Ctrl+点击：强制更新无变化的商品
                                        print(f"🔄 强制更新第{row+1}行：状态为无变化（Ctrl+点击）")
                                        # 降级为DEBUG级别，这是操作过程信息
                                        self.main_window.log_message(f"强制更新第{row+1}行（原状态：无变化）", "DEBUG")
                                    else:
                                        print(f"⏭️ 跳过第{row+1}行：状态为无变化（按住Ctrl键可强制更新）")
                                        continue

                                # 根据策略和颜色规格组织更新数据
                                if strategy == 'auto_update' and len(color_groups) == 1:
                                    # 单个颜色规格自动更新：批量更新所有尺码
                                    color_name = list(color_groups.keys())[0]
                                    color_skus = color_groups[color_name]

                                    update_item = {
                                        "row": row,
                                        "sku_code": sku_code,
                                        "sku_id": color_skus[0].get('sku_id', ''),  # 使用第一个SKU的ID作为代表
                                        "strategy": strategy,
                                        "ticket_price": ticket_price,
                                        "color_spec_skus": color_skus  # 完整的颜色规格SKU列表
                                    }
                                    update_items.append(update_item)
                                    print(f"准备批量更新第{row+1}行：{color_name} ({len(color_skus)}个尺码)")

                                elif match_result.get('confirmed', False):
                                    # 已确认的商品：使用选中的SKU列表
                                    selected_skus = match_result.get('selected_skus', [])
                                    if selected_skus:
                                        update_item = {
                                            "row": row,
                                            "sku_code": sku_code,
                                            "sku_id": selected_skus[0].get('sku_id', ''),
                                            "strategy": 'manual_confirm',
                                            "ticket_price": ticket_price,
                                            "color_spec_skus": selected_skus
                                        }
                                        update_items.append(update_item)
                                        print(f"准备更新已确认第{row+1}行：{len(selected_skus)}个SKU")
                                    else:
                                        print(f"跳过第{row+1}行：已确认但无选中SKU")
                                else:
                                    # 其他情况：需要用户先确认颜色规格
                                    print(f"跳过第{row+1}行：需要先确认颜色规格 (策略: {strategy})")
                            else:
                                print(f"跳过第{row+1}行：没有ERP匹配结果")
                    else:
                        print(f"⏭️ 跳过第{row+1}行：状态不符合更新条件：{status_text}")
            except Exception as e:
                self.main_window.log_message(f"准备更新第{row+1}行失败: {str(e)}", "ERROR")

        if not update_items:
            # 🔥 修复：提供更详细的用户指导和解决方案
            self.main_window.log_message("没有可更新的商品", "WARNING")

            # 检查是否有ERP匹配结果
            if not hasattr(self.main_window, 'erp_match_results') or not self.main_window.erp_match_results:
                self.main_window.log_message("💡 解决方案：请先点击'查询ERP'按钮获取商品信息", "INFO")
            else:
                # 统计各种状态的商品数量
                no_change_count = 0
                need_confirm_count = 0
                no_match_count = 0

                table = getattr(self.main_window, f'{table_type}_table', None)
                if table:
                    for row in range(table.rowCount()):
                        status_item = table.item(row, 6)  # 状态列
                        if status_item:
                            status_text = status_item.text()
                            if "无变化" in status_text:
                                no_change_count += 1
                            elif "待确认" in status_text:
                                need_confirm_count += 1
                            else:
                                no_match_count += 1

                # 提供具体的解决建议
                if no_change_count > 0:
                    self.main_window.log_message(f"📊 发现{no_change_count}个商品价格无变化，已自动跳过", "INFO")
                if need_confirm_count > 0:
                    self.main_window.log_message(f"💡 有{need_confirm_count}个商品需要确认颜色规格后才能上传", "INFO")
                if no_match_count > 0:
                    self.main_window.log_message(f"⚠️ 有{no_match_count}个商品未找到ERP匹配，请检查商品信息", "WARNING")

            return

        # 启动成本更新线程
        self.start_cost_update_thread(update_items, table_type)

    def start_cost_update_thread(self, update_items, table_type):
        """启动成本更新线程"""
        try:
            # 创建更新线程
            self.cost_update_thread = CostUpdateThread(
                update_items,
                self.erp_integration,
                table_type
            )
            
            # 连接信号
            self.cost_update_thread.progress_updated.connect(self._on_cost_update_progress)
            self.cost_update_thread.item_updated.connect(self._on_cost_item_updated)
            self.cost_update_thread.update_completed.connect(self._on_cost_update_completed)
            
            # 更新UI状态
            self.processing = True
            self._update_button_states(table_type, updating=True)
            
            # 启动线程
            self.cost_update_thread.start()

            # 显示统一进度条
            self.main_window.show_unified_progress("上传成本中...")

            self.main_window.log_message(f"开始更新{len(update_items)}个商品的成本价...")
            
        except Exception as e:
            self.main_window.log_message(f"启动成本更新失败: {str(e)}", "ERROR")
            self.processing = False

    def abort_erp_operations(self, table_type):
        """中止ERP操作"""
        try:
            aborted = False
            
            # 中止查询线程
            if self.erp_query_thread and self.erp_query_thread.isRunning():
                self.erp_query_thread.requestInterruption()
                self.erp_query_thread.wait(3000)  # 等待3秒
                aborted = True
                self.main_window.log_message("ERP查询已中止")
            
            # 中止更新线程
            if self.cost_update_thread and self.cost_update_thread.isRunning():
                self.cost_update_thread.requestInterruption()
                self.cost_update_thread.wait(3000)  # 等待3秒
                aborted = True
                self.main_window.log_message("成本更新已中止")
            
            if not aborted:
                self.main_window.log_message("没有正在进行的操作")
            
            # 重置状态
            self.processing = False
            self._reset_button_states(table_type)
            
        except Exception as e:
            self.main_window.log_message(f"中止操作失败: {str(e)}", "ERROR")

    def _update_button_states(self, table_type, querying=False, updating=False):
        """更新按钮状态"""
        buttons = self.main_window.table_ui_manager.get_erp_buttons_by_type(table_type)
        
        if querying:
            # 查询中：禁用查询按钮，启用中止按钮
            if 'query_erp' in buttons:
                buttons['query_erp'].setEnabled(False)
            if 'query_selected' in buttons:
                buttons['query_selected'].setEnabled(False)
            if 'update_erp' in buttons:
                buttons['update_erp'].setEnabled(False)
            if 'abort' in buttons:
                buttons['abort'].setEnabled(True)
        elif updating:
            # 更新中：禁用所有按钮，启用中止按钮
            if 'query_erp' in buttons:
                buttons['query_erp'].setEnabled(False)
            if 'query_selected' in buttons:
                buttons['query_selected'].setEnabled(False)
            if 'update_erp' in buttons:
                buttons['update_erp'].setEnabled(False)
            if 'abort' in buttons:
                buttons['abort'].setEnabled(True)

    def _reset_button_states(self, table_type):
        """重置按钮状态"""
        buttons = self.main_window.table_ui_manager.get_erp_buttons_by_type(table_type)
        
        if 'query_erp' in buttons:
            buttons['query_erp'].setEnabled(True)
        if 'query_selected' in buttons:
            buttons['query_selected'].setEnabled(True)
        if 'update_erp' in buttons:
            buttons['update_erp'].setEnabled(True)
        if 'abort' in buttons:
            buttons['abort'].setEnabled(False)

    # 线程信号处理方法
    def _on_erp_query_progress(self, current, total, message):
        """ERP查询进度更新"""
        self.main_window.log_message(f"ERP查询进度: {current}/{total} - {message}")

        # 更新统一进度条
        progress_percent = int((current / total) * 100) if total > 0 else 0
        self.main_window.update_unified_progress(progress_percent, f"ERP查询: {message}")

    def _on_row_status_updated(self, row, status):
        """表格行状态更新"""
        # 获取当前活动的表格
        current_table = self.main_window.table_ui_manager.get_current_table()
        if current_table and row < current_table.rowCount():
            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignCenter)
            current_table.setItem(row, 6, status_item)

    def _on_match_result_updated(self, row, status, strategy, confidence, recommended_sku_id, full_match_result):
        """匹配结果更新"""


        # 更新状态显示
        current_table = self.main_window.table_ui_manager.get_current_table()
        if current_table and row < current_table.rowCount():
            # 更新状态列，显示匹配结果
            combined_status = f"{status}"
            status_item = QTableWidgetItem(combined_status)
            status_item.setTextAlignment(Qt.AlignCenter)  # 🔥 修复：设置居中对齐
            current_table.setItem(row, 6, status_item)

            # 🔥 关键修复：保存完整的匹配结果到主窗口
            if full_match_result:
                print(f"💾 保存完整匹配结果：第{row+1}行")
                print(f"   - 策略: {full_match_result.get('strategy', 'N/A')}")
                print(f"   - 颜色分组数量: {len(full_match_result.get('color_groups', {}))}")
                print(f"   - 颜色分组: {list(full_match_result.get('color_groups', {}).keys())}")

                # 添加表格颜色规格信息
                color_spec_item = current_table.item(row, 2)  # 颜色规格列
                if color_spec_item:
                    full_match_result["table_color_spec"] = color_spec_item.text().strip()
                else:
                    full_match_result["table_color_spec"] = ""

                # 保存到主窗口的erp_match_results
                self.main_window.erp_match_results[row] = full_match_result
                print(f"✅ 匹配结果已保存到erp_match_results[{row}]")

                # 对于单个颜色规格且无确认商品卡的商品，直接计算利润
                self._auto_calculate_profit_for_single_color(row, full_match_result, current_table)

                # 计算并更新退货率列
                self._calculate_and_update_return_rate(row, full_match_result, current_table)

            else:
                print(f"⚠️ 没有完整匹配结果，保存基本信息：第{row+1}行")
                # 兼容旧版本，只存储基本信息
                self.main_window.erp_match_results[row] = {
                    "strategy": strategy,
                    "confidence": confidence,
                    "recommended_sku_id": recommended_sku_id,
                    "status": status,
                    "color_groups": {},
                    "table_color_spec": ""
                }

    def _on_erp_query_completed(self, success):
        """ERP查询完成"""
        self.processing = False
        
        if success:
            # 降级为DEBUG级别，用户在界面上已能看到查询完成状态
            self.main_window.log_message("ERP查询完成", "DEBUG")
            # 启用更新按钮
            current_table_type = self.main_window.table_ui_manager.get_current_table_type()
            buttons = self.main_window.table_ui_manager.get_erp_buttons_by_type(current_table_type)
            if 'update_erp' in buttons:
                buttons['update_erp'].setEnabled(True)
            
            # 🔥 修复：ERP查询完成后使用新的状态保存机制
            if hasattr(self.main_window, 'table_ui_manager'):
                # 降级为DEBUG级别的print输出
                # print(f"💾 ERP查询完成，使用新的状态保存机制...")
                # 延迟保存，确保所有匹配结果都已处理完成
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(1000, self.main_window.table_ui_manager.save_current_erp_state)
                    
        else:
            self.main_window.log_message("ERP查询失败", "ERROR")
        
        # 重置按钮状态
        current_table_type = self.main_window.table_ui_manager.get_current_table_type()
        self._reset_button_states(current_table_type)

        # 隐藏统一进度条
        self.main_window.hide_unified_progress()

    def _on_erp_query_selected_completed(self, success, table_type):
        """选中行ERP查询完成"""
        self.processing = False
        
        if success:
            self.main_window.log_message("选中商品ERP查询完成")
        else:
            self.main_window.log_message("选中商品ERP查询失败", "ERROR")
        
        # 重置按钮状态
        self._reset_button_states(table_type)

    def _on_cost_update_progress(self, current, total, message):
        """成本更新进度"""
        self.main_window.log_message(f"成本更新进度: {current}/{total} - {message}")

        # 更新统一进度条
        progress_percent = int((current / total) * 100) if total > 0 else 0
        self.main_window.update_unified_progress(progress_percent, f"成本上传: {message}")

    def _on_cost_item_updated(self, row, success, message, new_cost_price=None, old_cost_price=None):
        """成本更新项目完成"""
        # 🔥 修复：恢复表格状态更新，但使用新的状态格式（删除文字描述）
        if success:
            self.main_window.log_message(f"第{row+1}行成本价上传成功: {message}")

            # 更新表格状态显示
            current_table = self.main_window.table_ui_manager.get_current_table()
            if current_table and row < current_table.rowCount():
                status_item = QTableWidgetItem(message)
                status_item.setTextAlignment(Qt.AlignCenter)
                current_table.setItem(row, 6, status_item)

                # 应用新的颜色规则（字体颜色而非背景色）
                if "📈" in message:
                    status_item.setForeground(QColor(244, 67, 54))  # 红色字体（价格上涨）
                elif "📉" in message:
                    status_item.setForeground(QColor(76, 175, 80))  # 绿色字体（价格下降）
                elif "✅" in message:
                    status_item.setForeground(QColor(76, 175, 80))  # 绿色字体（成功）
                else:
                    status_item.setForeground(QColor(255, 255, 255))  # 白色字体（默认）
        else:
            self.main_window.log_message(f"第{row+1}行成本价上传失败: {message}", "ERROR")

            # 更新失败状态
            current_table = self.main_window.table_ui_manager.get_current_table()
            if current_table and row < current_table.rowCount():
                status_item = QTableWidgetItem(message)
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setForeground(QColor(244, 67, 54))  # 红色字体（错误）
                current_table.setItem(row, 6, status_item)

    def _on_cost_update_completed(self, success_count, total_count):
        """成本更新完成"""
        self.processing = False

        self.main_window.log_message(f"成本更新完成: {success_count}/{total_count} 成功")

        # 重置按钮状态
        current_table_type = self.main_window.table_ui_manager.get_current_table_type()
        self._reset_button_states(current_table_type)

        # 隐藏统一进度条
        self.main_window.hide_unified_progress()



    def _auto_calculate_profit_for_single_color(self, row, match_result, table):
        """为单个颜色规格且无确认商品卡的商品自动计算利润"""
        try:
            # 检查是否为单个颜色规格且策略为AUTO_UPDATE
            strategy = match_result.get('strategy', '')
            color_groups = match_result.get('color_groups', {})
            color_count = len(color_groups)

            if strategy == 'auto_update' and color_count == 1:
                print(f"🎯 检测到单个颜色规格商品，自动计算利润：第{row+1}行")

                # 获取票据价格（成本价）
                price_item = table.item(row, 4)  # 单价列
                if not price_item:
                    return

                try:
                    ticket_price = float(price_item.text().replace('¥', '').strip())
                except (ValueError, TypeError):
                    print(f"第{row+1}行：无法解析票据价格")
                    return

                # 获取第一个颜色规格的SKU列表
                color_name = list(color_groups.keys())[0]
                color_skus = color_groups[color_name]

                if not color_skus:
                    return

                # 取第一个SKU的售价作为代表
                first_sku = color_skus[0]
                sale_price = first_sku.get('sale_price', 0)

                try:
                    sale_price_float = float(sale_price)

                    # 计算利润：售价 - 100（固定扣除）- 表格成本价
                    profit = sale_price_float - 100 - ticket_price

                    # 格式化利润显示
                    profit_text = f"¥{profit:.0f}"

                    # 设置利润列（第7列）
                    profit_item = QTableWidgetItem(profit_text)
                    profit_item.setTextAlignment(Qt.AlignCenter)
                    profit_item.setFlags(profit_item.flags() & ~Qt.ItemIsEditable)

                    # 根据利润设置颜色，利润低于20的用红色显示
                    if profit < 20:
                        profit_item.setForeground(QColor(244, 67, 54))  # 红色
                    else:
                        profit_item.setForeground(QColor(76, 175, 80))  # 绿色

                    table.setItem(row, 7, profit_item)
                    print(f"第{row+1}行：自动利润计算完成 = {profit_text} (售价{sale_price_float} - 成本{ticket_price})")

                    # 保存利润数据到匹配结果中
                    if hasattr(self.main_window, 'erp_match_results') and row in self.main_window.erp_match_results:
                        self.main_window.erp_match_results[row]["profit_text"] = profit_text
                        self.main_window.erp_match_results[row]["profit_value"] = profit
                        self.main_window.erp_match_results[row]["sale_price"] = sale_price_float
                        self.main_window.erp_match_results[row]["cost_price"] = ticket_price
                        self.main_window.erp_match_results[row]["auto_calculated"] = True  # 标记为自动计算
                        print(f"💾 第{row+1}行：自动计算的利润数据已保存")

                except (ValueError, TypeError) as e:
                    print(f"第{row+1}行：自动利润计算价格转换失败: {e}")

        except Exception as e:
            print(f"自动利润计算失败: {str(e)}")

    def _calculate_and_update_return_rate(self, row, match_result, table):
        """计算并更新退货率列和30天实发列（使用优化的商品链接计算）"""
        try:
            color_groups = match_result.get('color_groups', {})
            if not color_groups:
                return

            # 收集所有SKU数据用于退货率计算
            all_skus = []
            for color_name, skus in color_groups.items():
                all_skus.extend(skus)

            if not all_skus:
                return

            # 降级为DEBUG级别的print输出，这是内部计算过程
            # print(f"🔍 ERP查询完成：第{row+1}行开始计算退货率和30天实发，SKU数量: {len(all_skus)}")

            # 🔥 使用优化的商品链接退货率计算（包含30天实发数据）
            from modules.return_rate_calculator import ReturnRateCalculator
            calculator = ReturnRateCalculator()
            link_rates = calculator.calculate_return_rates_by_product_links(all_skus)
            formatted_rates = calculator.format_multi_link_display(link_rates)

            # 降级为DEBUG级别的print输出
            # print(f"🔗 第{row+1}行：商品链接识别结果: {len(link_rates)} 个链接")
            # for link_id, rates in link_rates.items():
            #     print(f"   - {link_id}: 15天={rates.get('15天', 'None')}%, 30天={rates.get('30天', 'None')}%, sent_qty_30={rates.get('sent_qty_30', 0)}")

            # 更新表格退货率两列：15退货率(第8列)、30退货率(第9列)
            if row < table.rowCount():
                from PyQt5.QtWidgets import QTableWidgetItem
                from PyQt5.QtCore import Qt
                from PyQt5.QtGui import QColor

                periods = ["15天", "30天"]  # 🔥 删除7天
                for i, period in enumerate(periods):
                    col_index = 8 + i  # 第8、9列
                    rate_text = formatted_rates[period]

                    return_rate_item = QTableWidgetItem(rate_text)
                    return_rate_item.setTextAlignment(Qt.AlignCenter)
                    return_rate_item.setFlags(return_rate_item.flags() & ~Qt.ItemIsEditable)

                    # 设置颜色（使用统一的红色#ff3e66）
                    if rate_text != "-" and "|" not in rate_text:
                        try:
                            rate_value = float(rate_text.replace("%", ""))
                            if rate_value < 20:
                                color = "#4CAF50"  # 绿色
                            elif rate_value < 50:
                                color = "#FF9800"  # 橙色
                            else:
                                color = "#ff3e66"  # 统一红色
                            return_rate_item.setForeground(QColor(color))
                        except:
                            pass

                    table.setItem(row, col_index, return_rate_item)

                # 更新30天实发列（第10列），使用多链接格式化
                sent_qty_text = calculator.format_multi_link_sent_qty_30_display(link_rates)

                sent_item = QTableWidgetItem(sent_qty_text)
                sent_item.setTextAlignment(Qt.AlignCenter)
                sent_item.setFlags(sent_item.flags() & ~Qt.ItemIsEditable)
                sent_item.setForeground(QColor(255, 255, 255))  # 白色字体
                table.setItem(row, 10, sent_item)

            # 保存退货率数据到匹配结果中
            match_result["link_rates"] = link_rates
            match_result["formatted_rates"] = formatted_rates
            # 为了兼容性，保存旧格式的显示文本
            summary = calculator.get_return_rate_summary_from_links(link_rates)
            match_result["return_rate_text"] = summary
            print(f"💾 第{row+1}行：商品链接退货率数据已保存到匹配结果")

        except Exception as e:
            print(f"计算退货率失败: {str(e)}")