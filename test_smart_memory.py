#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能记忆功能测试脚本
"""

import os
import sys
import json
from datetime import datetime

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.smart_memory_manager import SmartMemoryManager


def test_smart_memory_manager():
    """测试智能记忆管理器"""
    print("🧠 开始测试智能记忆管理器...")
    
    # 创建测试用的记忆管理器
    test_memory_file = "test_smart_memory.json"
    if os.path.exists(test_memory_file):
        os.remove(test_memory_file)
    
    manager = SmartMemoryManager(test_memory_file)
    
    # 测试1: 添加临时记忆
    print("\n📝 测试1: 添加临时记忆")
    memory_key1 = manager.add_temp_memory(
        sku_code="7922",
        color_spec="套装",
        confirmed_colors=["两件套装"],
        supplier="GT408",
        file_pattern="微信图片",
        notes="测试记忆1"
    )
    print(f"✅ 添加临时记忆成功: {memory_key1}")
    
    memory_key2 = manager.add_temp_memory(
        sku_code="1359",
        color_spec="连衣裙",
        confirmed_colors=["白色吊带裙", "连衣裙"],
        supplier="GT251",
        file_pattern="微信图片",
        notes="测试记忆2"
    )
    print(f"✅ 添加临时记忆成功: {memory_key2}")
    
    # 测试2: 提交临时记忆
    print("\n💾 测试2: 提交临时记忆")
    success = manager.commit_temp_memories()
    print(f"✅ 提交记忆结果: {success}")
    
    # 测试3: 查找匹配记忆
    print("\n🔍 测试3: 查找匹配记忆")
    matching_memory = manager.find_matching_memory("7922", "套装")
    if matching_memory:
        print(f"✅ 找到匹配记忆: {matching_memory.sku_code}|{matching_memory.color_spec}")
        print(f"   确认颜色: {matching_memory.confirmed_colors}")
        print(f"   使用次数: {matching_memory.usage_count}")
    else:
        print("❌ 未找到匹配记忆")
    
    # 测试4: 更新使用统计
    print("\n📊 测试4: 更新使用统计")
    success = manager.update_memory_usage("7922", "套装")
    print(f"✅ 更新使用统计结果: {success}")
    
    # 测试5: 获取统计信息
    print("\n📈 测试5: 获取统计信息")
    stats = manager.get_memory_statistics()
    print(f"✅ 总记忆数量: {stats.get('total_memories', 0)}")
    print(f"✅ 总使用次数: {stats.get('total_usage', 0)}")
    print(f"✅ 临时记忆: {stats.get('temp_memories', 0)}")
    
    # 测试6: 导出记忆
    print("\n📤 测试6: 导出记忆")
    export_file = "test_memory_export.json"
    success = manager.export_memories(export_file)
    print(f"✅ 导出记忆结果: {success}")
    
    if success and os.path.exists(export_file):
        with open(export_file, 'r', encoding='utf-8') as f:
            export_data = json.load(f)
        print(f"✅ 导出文件包含 {len(export_data.get('memories', {}))} 条记忆")
    
    # 测试7: 删除记忆
    print("\n🗑️ 测试7: 删除记忆")
    success = manager.delete_memory(memory_key2)
    print(f"✅ 删除记忆结果: {success}")
    
    # 测试8: 更新设置
    print("\n⚙️ 测试8: 更新设置")
    new_settings = {
        "enabled": True,
        "auto_save_on_upload": True,
        "auto_confirm_threshold": 0.9,
        "max_memories": 500
    }
    success = manager.update_settings(new_settings)
    print(f"✅ 更新设置结果: {success}")
    print(f"✅ 新的自动确认阈值: {manager.settings.get('auto_confirm_threshold', 0.8)}")
    
    # 清理测试文件
    print("\n🧹 清理测试文件")
    for test_file in [test_memory_file, export_file]:
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"✅ 已删除: {test_file}")
    
    print("\n🎉 智能记忆管理器测试完成！")


def test_memory_integration():
    """测试记忆集成功能"""
    print("\n🔗 测试记忆集成功能...")
    
    # 模拟颜色确认场景
    test_scenarios = [
        {
            "sku_code": "7922",
            "color_spec": "套装",
            "confirmed_colors": ["两件套装"],
            "description": "两件套装商品"
        },
        {
            "sku_code": "1359", 
            "color_spec": "连衣裙",
            "confirmed_colors": ["白色吊带裙", "连衣裙"],
            "description": "连衣裙商品"
        },
        {
            "sku_code": "1096",
            "color_spec": "蓝色",
            "confirmed_colors": ["蓝色"],
            "description": "蓝色商品"
        }
    ]
    
    manager = SmartMemoryManager("test_integration_memory.json")
    
    print("📝 模拟用户确认颜色过程...")
    for i, scenario in enumerate(test_scenarios):
        print(f"\n场景 {i+1}: {scenario['description']}")
        
        # 添加临时记忆
        memory_key = manager.add_temp_memory(
            sku_code=scenario["sku_code"],
            color_spec=scenario["color_spec"],
            confirmed_colors=scenario["confirmed_colors"],
            supplier="TEST",
            file_pattern="test_image.jpg",
            notes=f"测试场景{i+1}"
        )
        print(f"  ✅ 添加临时记忆: {memory_key}")
    
    print("\n💾 模拟用户上传操作...")
    success = manager.commit_temp_memories()
    print(f"✅ 提交所有记忆: {success}")
    
    print("\n🔍 模拟下次加载相同商品...")
    for scenario in test_scenarios:
        matching_memory = manager.find_matching_memory(
            scenario["sku_code"], 
            scenario["color_spec"]
        )
        
        if matching_memory:
            print(f"  🧠 自动匹配: {scenario['sku_code']}|{scenario['color_spec']}")
            print(f"     推荐颜色: {matching_memory.confirmed_colors}")
            print(f"     置信度: {matching_memory.confidence:.2f}")
        else:
            print(f"  ❌ 未找到匹配: {scenario['sku_code']}|{scenario['color_spec']}")
    
    # 清理测试文件
    test_file = "test_integration_memory.json"
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n🧹 已清理测试文件: {test_file}")
    
    print("\n🎉 记忆集成测试完成！")


if __name__ == "__main__":
    print("🚀 开始智能记忆功能测试")
    print("=" * 50)
    
    try:
        # 测试基础功能
        test_smart_memory_manager()
        
        # 测试集成功能
        test_memory_integration()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！智能记忆功能正常工作。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
