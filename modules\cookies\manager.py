"""
Cookies管理器模块 - 负责cookies的文件操作和状态管理
"""

import json
import os
from datetime import datetime
from typing import Dict, Optional, Tuple, Callable
from .exceptions import CookiesFileError


class CookiesManager:
    """Cookies管理器 - 处理cookies的文件操作和状态管理"""
    
    def __init__(self, cookies_file: str = "latest_cookies.json", log_callback: Optional[Callable] = None):
        self.cookies_file = cookies_file
        self.log_callback = log_callback or (lambda msg, level="INFO": print(f"[{level}] {msg}"))
        self.current_cookies = {}
        self.last_load_time = None
        self.last_save_time = None
        self.last_validation_time = None
        self.validation_status = "unknown"  # unknown, valid, invalid
        
    def load_cookies(self) -> Dict[str, str]:
        """
        从文件加载cookies
        
        Returns:
            加载的cookies字典
            
        Raises:
            CookiesFileError: 文件操作失败时抛出
        """
        try:
            if not os.path.exists(self.cookies_file):
                self.log_callback(f"Cookies文件不存在: {self.cookies_file}", "WARNING")
                return {}
            
            with open(self.cookies_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, dict):
                raise CookiesFileError("Cookies文件格式错误，应为JSON对象")
            
            # 转换所有值为字符串
            self.current_cookies = {str(k): str(v) for k, v in data.items()}
            self.last_load_time = datetime.now()
            
            # 降级为DEBUG级别，避免在界面上重复显示
            self.log_callback(f"成功加载 {len(self.current_cookies)} 个cookies", "DEBUG")
            return self.current_cookies.copy()
            
        except json.JSONDecodeError as e:
            raise CookiesFileError(f"Cookies文件JSON格式错误: {str(e)}")
        except Exception as e:
            raise CookiesFileError(f"加载cookies文件失败: {str(e)}")
    
    def save_cookies(self, cookies: Dict[str, str]) -> bool:
        """
        保存cookies到文件

        Args:
            cookies: 要保存的cookies字典

        Returns:
            是否保存成功

        Raises:
            CookiesFileError: 文件操作失败时抛出
        """
        try:
            # 保存新的cookies
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)

            self.current_cookies = cookies.copy()
            self.last_save_time = datetime.now()

            self.log_callback(f"成功保存 {len(cookies)} 个cookies到 {self.cookies_file}")
            return True

        except Exception as e:
            raise CookiesFileError(f"保存cookies文件失败: {str(e)}")
    
    def get_current_cookies(self) -> Dict[str, str]:
        """获取当前cookies"""
        return self.current_cookies.copy()
    
    def update_cookies(self, cookies: Dict[str, str]) -> None:
        """更新当前cookies（不保存到文件）"""
        self.current_cookies = cookies.copy()
        self.validation_status = "unknown"  # 重置验证状态
    
    def clear_cookies(self) -> None:
        """清空当前cookies"""
        self.current_cookies = {}
        self.validation_status = "unknown"
        self.last_validation_time = None
    
    def get_status_info(self) -> Dict[str, any]:
        """
        获取cookies状态信息
        
        Returns:
            状态信息字典
        """
        return {
            'cookies_count': len(self.current_cookies),
            'validation_status': self.validation_status,
            'last_load_time': self.last_load_time,
            'last_save_time': self.last_save_time,
            'last_validation_time': self.last_validation_time,
            'file_exists': os.path.exists(self.cookies_file),
            'file_size': os.path.getsize(self.cookies_file) if os.path.exists(self.cookies_file) else 0
        }
    
    def update_validation_status(self, status: str, message: str = "") -> None:
        """
        更新验证状态
        
        Args:
            status: 状态 ('valid', 'invalid', 'unknown')
            message: 状态消息
        """
        self.validation_status = status
        self.last_validation_time = datetime.now()
        
        status_map = {
            'valid': '有效',
            'invalid': '无效',
            'unknown': '未知'
        }
        
        status_text = status_map.get(status, status)
        # 降级为DEBUG级别，避免在界面上重复显示状态信息
        if message:
            self.log_callback(f"Cookies状态更新: {status_text} - {message}", "DEBUG")
        else:
            self.log_callback(f"Cookies状态更新: {status_text}", "DEBUG")
    
    def export_cookies(self, export_file: str, format_type: str = 'json') -> bool:
        """
        导出cookies到指定文件
        
        Args:
            export_file: 导出文件路径
            format_type: 导出格式 ('json', 'browser', 'keyvalue')
            
        Returns:
            是否导出成功
        """
        try:
            if not self.current_cookies:
                raise CookiesFileError("没有可导出的cookies")
            
            if format_type == 'json':
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(self.current_cookies, f, ensure_ascii=False, indent=2)
            
            elif format_type == 'browser':
                browser_format = '; '.join([f"{k}={v}" for k, v in self.current_cookies.items()])
                with open(export_file, 'w', encoding='utf-8') as f:
                    f.write(browser_format)
            
            elif format_type == 'keyvalue':
                keyvalue_format = '\n'.join([f"{k}: {v}" for k, v in self.current_cookies.items()])
                with open(export_file, 'w', encoding='utf-8') as f:
                    f.write(keyvalue_format)
            
            else:
                raise CookiesFileError(f"不支持的导出格式: {format_type}")
            
            self.log_callback(f"成功导出cookies到 {export_file} ({format_type}格式)")
            return True
            
        except Exception as e:
            raise CookiesFileError(f"导出cookies失败: {str(e)}")
    
    def import_cookies_from_file(self, import_file: str) -> Dict[str, str]:
        """
        从文件导入cookies
        
        Args:
            import_file: 导入文件路径
            
        Returns:
            导入的cookies字典
            
        Raises:
            CookiesFileError: 文件操作失败时抛出
        """
        try:
            if not os.path.exists(import_file):
                raise CookiesFileError(f"导入文件不存在: {import_file}")
            
            with open(import_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 尝试解析为JSON
            try:
                data = json.loads(content)
                if isinstance(data, dict):
                    cookies = {str(k): str(v) for k, v in data.items()}
                    self.log_callback(f"成功从 {import_file} 导入 {len(cookies)} 个cookies (JSON格式)")
                    return cookies
            except json.JSONDecodeError:
                pass
            
            # 如果不是JSON，需要使用解析器处理
            from .parser import CookiesParser
            parser = CookiesParser()
            cookies = parser.parse(content)
            
            self.log_callback(f"成功从 {import_file} 导入 {len(cookies)} 个cookies")
            return cookies
            
        except Exception as e:
            raise CookiesFileError(f"导入cookies文件失败: {str(e)}")
    

