#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用AI处理模块
支持多种AI API格式：OpenAI、xAI、<PERSON>、国产大模型等
"""

import json
import base64
from typing import Dict, Optional, Callable
import requests

class AIProcessor:
    """通用AI处理器"""
    
    def __init__(self, api_key: str = "", api_url: str = "", model_name: str = "",
                 prompt_template: str = "", log_callback: Optional[Callable] = None):
        self.api_key = api_key
        self.api_url = api_url
        self.model_name = model_name
        self.prompt_template = prompt_template
        self.log_callback = log_callback or self._default_log
    
    def _default_log(self, message: str):
        """默认日志输出"""
        print(f"[AIProcessor] {message}")
    
    def log(self, message: str):
        """记录日志"""
        self.log_callback(message)
    
    def update_config(self, api_key: str, api_url: str, model_name: str = "", prompt_template: str = None):
        """更新AI配置"""
        self.api_key = api_key
        self.api_url = api_url
        if model_name:
            self.model_name = model_name
        if prompt_template:
            self.prompt_template = prompt_template

    def configure(self, config_dict: dict):
        """配置AI处理器（兼容方法）"""
        self.api_key = config_dict.get('api_key', '')
        self.api_url = config_dict.get('api_url', '')
        self.model_name = config_dict.get('model_name', '')
        self.prompt_template = config_dict.get('prompt_template', '')

    def process_image(self, image_path: str, prompt: str = None) -> dict:
        """处理图像（兼容方法）"""
        try:
            # 如果提供了prompt，使用它；否则使用默认的prompt_template
            if prompt:
                original_prompt = self.prompt_template
                self.prompt_template = prompt

            # 调用analyze_ticket方法
            ticket_data, ai_response = self.analyze_ticket(image_path)

            # 恢复原始prompt
            if prompt:
                self.prompt_template = original_prompt

            # 返回结果
            if ticket_data:
                return {
                    "success": True,
                    "data": ticket_data,
                    "ai_response": ai_response,
                    "file_path": image_path
                }
            else:
                return {
                    "success": False,
                    "error": "AI解析失败",
                    "ai_response": ai_response,
                    "file_path": image_path
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "file_path": image_path
            }
    
    def detect_api_type(self) -> str:
        """根据API URL自动检测API类型"""
        if not self.api_url:
            return "unknown"
        
        url_lower = self.api_url.lower()
        
        if "openai.com" in url_lower:
            return "openai"
        elif "x.ai" in url_lower or "xai" in url_lower:
            return "xai"
        elif "anthropic.com" in url_lower:
            return "claude"
        elif "baidu.com" in url_lower or "wenxin" in url_lower:
            return "wenxin"
        elif "aliyun.com" in url_lower or "dashscope" in url_lower:
            return "qwen"
        elif "moonshot.cn" in url_lower:
            return "moonshot"
        elif "volces.com" in url_lower or "doubao" in url_lower:
            return "doubao"
        else:
            return "generic"

    def get_default_model(self, api_type: str) -> str:
        """获取API类型的默认模型名称"""
        default_models = {
            "openai": "gpt-4-vision-preview",
            "xai": "grok-vision-beta",
            "claude": "claude-3-vision-20240229",
            "doubao": "doubao-1-5-vision-pro-32k-250115",
            "wenxin": "ernie-bot-4",
            "qwen": "qwen-vl-plus",
            "moonshot": "moonshot-v1-8k",
            "generic": "gpt-4-vision-preview"
        }
        return default_models.get(api_type, "gpt-4-vision-preview")

    def encode_image(self, image_path: str) -> tuple[Optional[str], str]:
        """将图像编码为base64格式"""
        try:
            with open(image_path, "rb") as image_file:
                base64_data = base64.b64encode(image_file.read()).decode('utf-8')

                # 根据文件扩展名确定MIME类型
                file_ext = image_path.lower().split('.')[-1]
                if file_ext in ['jpg', 'jpeg']:
                    mime_type = 'image/jpeg'
                elif file_ext == 'png':
                    mime_type = 'image/png'
                elif file_ext == 'gif':
                    mime_type = 'image/gif'
                elif file_ext == 'webp':
                    mime_type = 'image/webp'
                else:
                    mime_type = 'image/jpeg'  # 默认

                return base64_data, mime_type
        except Exception as e:
            self.log(f"图像编码失败: {str(e)}")
            return None, ""
    
    def build_request_payload(self, base64_image: str, mime_type: str, api_type: str) -> Dict:
        """根据API类型构建请求载荷"""

        # 使用用户指定的模型名称，如果没有则使用默认值
        model_name = self.model_name if self.model_name else self.get_default_model(api_type)

        if api_type == "openai":
            return {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.prompt_template},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{mime_type};base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.1
            }
        
        elif api_type == "xai":
            return {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.prompt_template},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{mime_type};base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.1
            }
        
        elif api_type == "claude":
            return {
                "model": model_name,
                "max_tokens": 1000,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.prompt_template},
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": mime_type,
                                    "data": base64_image
                                }
                            }
                        ]
                    }
                ]
            }

        elif api_type == "doubao":
            return {
                "model": model_name,
                "max_tokens": 4096,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.prompt_template},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{mime_type};base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.1,
                "stream": False
            }
        
        else:  # generic格式，适用于大多数兼容OpenAI的API
            return {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": self.prompt_template},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{mime_type};base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.1
            }
    
    def extract_response_content(self, result: Dict, api_type: str) -> str:
        """根据API类型提取响应内容"""
        try:
            if api_type == "claude":
                return result.get("content", [{}])[0].get("text", "")
            else:  # OpenAI格式 (包括xAI、generic等)
                return result.get("choices", [{}])[0].get("message", {}).get("content", "")
        except Exception as e:
            self.log(f"提取响应内容失败: {str(e)}")
            return ""
    
    def parse_ai_response(self, ai_response: str) -> Optional[Dict]:
        """解析AI响应为JSON格式"""
        try:
            # 清理响应内容
            ai_response = ai_response.strip()

            # 移除markdown代码块标记
            if ai_response.startswith("```json"):
                ai_response = ai_response[7:-3].strip()
            elif ai_response.startswith("```"):
                ai_response = ai_response[3:-3].strip()

            # 尝试直接解析JSON
            try:
                ticket_data = json.loads(ai_response)
                return ticket_data
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试从文本中提取JSON
                return self.extract_json_from_text(ai_response)

        except Exception as e:
            self.log(f"响应解析异常: {str(e)}")
            return None

    def extract_json_from_text(self, text: str) -> Optional[Dict]:
        """从文本中提取JSON格式数据"""
        try:
            # 查找JSON代码块
            import re

            # 查找```json...```格式
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)

            # 查找```...```格式
            code_match = re.search(r'```\s*(\{.*?\})\s*```', text, re.DOTALL)
            if code_match:
                json_str = code_match.group(1)
                return json.loads(json_str)

            # 查找纯JSON格式（以{开头，以}结尾）
            json_match = re.search(r'\{.*\}', text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)

            # 如果都找不到，尝试手动构建JSON（基于豆包的输出格式）
            return self.build_json_from_description(text)

        except Exception as e:
            # 保留ERROR级别，这是重要错误信息
            self.log(f"从文本提取JSON失败: {str(e)}")
            self.log(f"AI返回内容: {text}")
            return None

    def build_json_from_description(self, text: str) -> Optional[Dict]:
        """从描述文本构建JSON（针对豆包的输出格式）"""
        try:
            # 这是一个备用方案，从豆包的描述中提取信息
            import re

            # 提取供应商
            supplier_patterns = [
                r'([\u4e00-\u9fa5]{2,8}(?:服饰|批发|公司|店铺|商店))',  # 中文+关键词
                r'这是一张([\u4e00-\u9fa5]{2,8})(?:服饰|批发|公司|店)',  # "这是一张XX服饰"
                r'([\u4e00-\u9fa5]{2,8})(?:的|批发|服饰).*?(?:单|票据)',  # XX的单据
            ]

            supplier = "未知供应商"
            for pattern in supplier_patterns:
                match = re.search(pattern, text)
                if match:
                    supplier = match.group(1)
                    break

            # 提取日期
            date_match = re.search(r'(\d{4})[年-](\d{1,2})[月-](\d{1,2})', text)
            if date_match:
                year, month, day = date_match.groups()
                date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            else:
                date = "2025-01-01"

            # 判断类型（如果有负数就是退货）
            ticket_type = "退货" if "退货" in text or "-" in text else "销售"

            # 提取商品信息（这里简化处理，实际应该更复杂）
            items = []
            # 查找款号和价格信息
            item_matches = re.findall(r'(\d+)款.*?数量[：:]?(-?\d+).*?单价[：:]?(\d+).*?小计[：:]?(-?\d+)', text)

            for match in item_matches:
                款号, 数量, 单价, 小计 = match
                items.append({
                    "款号": 款号,
                    "颜色规格": "未知",
                    "数量": int(数量),
                    "单价": int(单价),
                    "小计": int(小计)
                })

            if not items:
                # 如果没有找到商品，创建一个示例
                items = [{
                    "款号": "未知",
                    "颜色规格": "未知",
                    "数量": 1,
                    "单价": 0,
                    "小计": 0
                }]

            return {
                "supplier": supplier,
                "date": date,
                "type": ticket_type,
                "items": items
            }

        except Exception as e:
            self.log(f"构建JSON失败: {str(e)}")
            return None
    
    def analyze_ticket(self, image_path: str) -> tuple[Optional[Dict], str]:
        """
        分析票据图像 - 支持多种AI API

        Args:
            image_path: 票据图像路径

        Returns:
            (解析后的票据数据字典, AI原始响应文本)
        """
        try:
            self.log(f"开始分析票据: {image_path}")
            
            # 编码图像
            base64_image, mime_type = self.encode_image(image_path)
            if not base64_image:
                return None, ""
            
            # 检测API类型
            api_type = self.detect_api_type()
            self.log(f"检测到API类型: {api_type}")
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # 根据API类型构建请求载荷
            payload = self.build_request_payload(base64_image, mime_type, api_type)
            
            # 发送API请求
            self.log("正在调用AI API...")
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 根据API类型解析响应
                ai_response = self.extract_response_content(result, api_type)
                
                if not ai_response:
                    # 保留ERROR级别，这是重要错误信息
                    self.log("AI返回内容为空")
                    return None, ""

                # 解析JSON
                ticket_data = self.parse_ai_response(ai_response)
                if ticket_data:
                    # 降级为DEBUG级别，这是分析结果统计
                    # self.log(f"AI分析成功: 供应商={ticket_data.get('supplier', '未知')}, 商品数量={len(ticket_data.get('items', []))}")
                    return ticket_data, ai_response
                else:
                    return None, ai_response
            else:
                self.log(f"API请求失败: {response.status_code} - {response.text}")
                return None, ""

        except Exception as e:
            self.log(f"AI分析失败: {str(e)}")
            return None, ""
    
    def validate_ticket_data(self, ticket_data: Dict) -> tuple[bool, str]:
        """验证票据数据格式"""
        try:
            if not isinstance(ticket_data, dict):
                return False, "数据格式错误：不是字典类型"
            
            required_fields = ["supplier", "date", "type", "items"]
            for field in required_fields:
                if field not in ticket_data:
                    return False, f"缺少必需字段: {field}"
            
            items = ticket_data.get("items", [])
            if not isinstance(items, list):
                return False, "商品明细格式错误：不是列表类型"
            
            if not items:
                return False, "商品明细为空"
            
            # 验证商品明细格式
            for i, item in enumerate(items):
                if not isinstance(item, dict):
                    return False, f"商品明细 {i+1} 格式错误：不是字典类型"
                
                item_required_fields = ["款号", "数量", "单价", "小计"]
                for field in item_required_fields:
                    if field not in item:
                        return False, f"商品明细 {i+1} 缺少字段: {field}"
            
            return True, "数据格式验证通过"
            
        except Exception as e:
            return False, f"验证异常: {str(e)}"
