"""
Cookies模块异常定义
"""


class CookiesError(Exception):
    """Cookies模块基础异常"""
    pass


class CookiesParseError(CookiesError):
    """Cookies解析异常"""
    
    def __init__(self, message: str, format_hint: str = None):
        super().__init__(message)
        self.format_hint = format_hint


class CookiesValidationError(CookiesError):
    """Cookies验证异常"""
    
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.error_code = error_code


class CookiesFileError(CookiesError):
    """Cookies文件操作异常"""
    pass
